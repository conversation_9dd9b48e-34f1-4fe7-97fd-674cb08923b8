<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me - الملف الشخصي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            overflow-x: hidden;
            max-width: 100%;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f0f2f5;
            direction: rtl;
            color: #333;
        }

        /* الشريط العلوي */
        .header {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 1000 !important;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100% !important;
        }

        .app-name {
            font-size: 40px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
            max-width: 600px;
            margin: 0 30px;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 12px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.15);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            outline: none;
            color: white;
            transition: all 0.3s ease;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
            font-weight: 500;
        }

        .search-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: white;
            font-size: 24px;
            padding: 10px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            position: relative;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        /* شريط التنقل */
        .nav-bar {
            background: white;
            position: fixed !important;
            top: 70px !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 999 !important;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            width: 100% !important;
        }

        .nav-items {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 25px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.3s;
            color: #495057;
            font-weight: 500;
            position: relative;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            color: #16CCC8;
            transform: translateY(-2px);
        }

        .nav-item:hover i {
            transform: scale(1.1);
            color: #16CCC8;
        }

        .nav-item i {
            font-size: 24px;
            transition: all 0.3s ease;
        }

        .nav-item.home-item {
            margin-left: 80px;
        }

        .nav-item.home-item i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item.location-item {
            margin-right: auto;
            background: rgba(22, 204, 200, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(22, 204, 200, 0.3);
        }

        .nav-item.location-item:hover {
            background: rgba(22, 204, 200, 0.2);
            border-color: rgba(22, 204, 200, 0.5);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 0;
            background: #f0f2f5;
            min-height: calc(100vh - 130px);
        }

        /* مودال البروفايل الشخصي */
        .my-profile-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 5000;
            display: block;
            overflow-y: auto;
        }

        .my-profile-content {
            width: 100%;
            min-height: 100vh;
            background: #f0f2f5;
            overflow-y: auto;
        }

        /* رأس البروفايل */
        .my-profile-header {
            position: relative;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            text-align: center;
            border-radius: 0 0 20px 20px;
            padding: 40px 20px;
            margin-top: 0;
            margin-bottom: 0;
        }

        /* زر الإغلاق البسيط */
        .close-profile-btn-simple {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .close-profile-btn-simple:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* صورة البروفايل في الأعلى */
        .profile-avatar-top-center {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
            padding: 10px;
        }

        .profile-avatar-extra-large {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            position: relative;
            border: 8px solid #4CAF50;
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            z-index: 10;
        }

        .profile-avatar-extra-large.online {
            border: 8px solid #4CAF50;
            box-shadow: 0 12px 35px rgba(0,0,0,0.4), 0 0 20px rgba(76, 175, 80, 0.5);
        }

        .profile-avatar-extra-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* معلومات البروفايل في الجزء الملون الكامل */
        .profile-info-colored-full {
            padding: 25px 30px 20px 30px;
            text-align: center;
            color: white;
            position: relative;
            z-index: 5;
        }

        .profile-name-white {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin: 0 0 20px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* النجوم الصفراء في الجزء الملون */
        .profile-rating-stars-colored {
            margin: 15px 0;
        }

        .stars-container-white {
            display: inline-flex;
            gap: 5px;
            margin-bottom: 8px;
        }

        .star-yellow-large {
            font-size: 24px;
            color: #FFD700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .star-yellow-large.empty {
            color: rgba(255, 255, 255, 0.3);
        }

        .rating-text-white {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        /* الإحصائيات في الجزء الملون */
        .profile-stats-colored {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding: 15px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .stat-item-colored {
            text-align: center;
            flex: 1;
        }

        .stat-number-white {
            font-size: 24px;
            font-weight: bold;
            color: white;
            display: block;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .stat-label-white {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 5px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* شريط التنقل */
        .profile-nav {
            display: flex;
            background: white;
            border-bottom: 1px solid #e1e8ed;
            overflow: visible;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1;
            border-radius: 20px 20px 0 0;
            padding-top: 20px;
        }

        .profile-nav-item {
            padding: 15px 25px;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 15px;
        }

        .profile-nav-item:hover {
            color: #16CCC8;
            background: rgba(22, 204, 200, 0.05);
        }

        .profile-nav-item.active {
            color: #16CCC8;
            border-bottom-color: #16CCC8;
            background: rgba(22, 204, 200, 0.1);
        }

        /* محتوى البروفايل */
        .profile-content-area {
            display: flex;
            gap: 20px;
            padding: 20px;
            background: #f0f2f5;
            min-height: calc(100vh - 200px);
        }

        .profile-sidebar {
            width: 420px;
            flex-shrink: 0;
        }

        .profile-main-content {
            flex: 1;
        }

        /* بطاقات الشريط الجانبي */
        .info-card, .photos-card, .followers-card, .projects-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-title i {
            color: #16CCC8;
            font-size: 20px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item i {
            color: #16CCC8;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .info-item span {
            color: #333;
            font-size: 14px;
        }

        /* منشورات البروفايل */
        .my-posts-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .post-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
        }

        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .post-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .post-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
        }

        .post-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .post-info h4 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .post-time {
            color: #666;
            font-size: 12px;
        }

        .post-content {
            color: #333;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .post-actions {
            display: flex;
            justify-content: space-around;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .post-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            font-size: 14px;
        }

        .post-action:hover {
            background: rgba(22, 204, 200, 0.1);
            color: #16CCC8;
        }

        .post-action i {
            font-size: 16px;
        }

        /* الصور في الشريط الجانبي */
        .photos-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .photo-item {
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
        }

        .photo-item:hover {
            transform: scale(1.05);
        }

        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* المتابعين */
        .followers-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .follower-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px;
            border-radius: 10px;
            transition: all 0.3s;
        }

        .follower-item:hover {
            background: rgba(22, 204, 200, 0.05);
        }

        .follower-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
        }

        .follower-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .follower-info {
            flex: 1;
        }

        .follower-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .follower-status {
            font-size: 12px;
            color: #666;
        }

        .follow-btn {
            padding: 6px 12px;
            border: 1px solid #16CCC8;
            background: transparent;
            color: #16CCC8;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .follow-btn:hover {
            background: #16CCC8;
            color: white;
        }

        /* شريط المتابعين */
        .followers-bar {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e8ed;
            overflow-x: auto;
            white-space: nowrap;
        }

        .followers-bar-content {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .follower-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
            flex-shrink: 0;
            border: 3px solid #16CCC8;
        }

        .follower-circle:hover {
            transform: scale(1.1);
        }

        .follower-circle img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .follower-item-bar {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .follower-name-bar {
            font-size: 12px;
            color: #333;
            font-weight: 500;
            text-align: center;
            max-width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .follower-item-bar {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            flex-shrink: 0;
            margin-left: 10px;
        }

        .follower-circle {
            width: 50px;
            height: 50px;
        }

        /* شريط القصص */
        .stories-bar {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e8ed;
            margin-bottom: 20px;
        }

        .stories-bar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .stories-bar-header h3 {
            color: #333;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stories-bar-header i {
            color: #16CCC8;
        }

        .add-story-btn {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .add-story-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(22, 204, 200, 0.4);
        }

        .stories-container {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding-bottom: 10px;
        }

        .story-item {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
            flex-shrink: 0;
            border: 3px solid #16CCC8;
            position: relative;
        }

        .story-item:hover {
            transform: scale(1.1);
        }

        .story-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .story-add {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        /* نافذة إنشاء المنشور */
        .create-post-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .create-post-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .create-post-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
        }

        .create-post-input {
            flex: 1;
            padding: 12px 20px;
            border: 1px solid #e1e8ed;
            border-radius: 25px;
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            outline: none;
            background: #f8f9fa;
        }

        .create-post-input:focus {
            border-color: #16CCC8;
            background: white;
        }

        .create-post-actions {
            display: flex;
            justify-content: space-around;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .create-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .create-action:hover {
            background: rgba(22, 204, 200, 0.1);
            color: #16CCC8;
        }

        .create-action i {
            font-size: 18px;
        }

        /* معلومات العمل المفصلة */
        .work-details-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .work-detail-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 6px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .work-detail-item:last-child {
            border-bottom: none;
        }

        .work-detail-icon {
            color: #16CCC8;
            font-size: 14px;
            width: 16px;
            text-align: center;
        }

        .work-detail-content {
            flex: 1;
        }

        .work-detail-title {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .work-detail-value {
            font-size: 13px;
            font-weight: 600;
            color: #333;
        }

        /* المشاريع المنجزة */
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .project-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .project-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
        }

        .project-content {
            padding: 20px;
        }

        .project-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .project-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .project-detail {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .project-detail i {
            color: #16CCC8;
            width: 16px;
        }

        /* الشهادات */
        .certificates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .certificate-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s;
        }

        .certificate-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .certificate-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }

        .certificate-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .certificate-issuer {
            font-size: 14px;
            opacity: 0.9;
        }

        /* المشاريع المنجزة - مستطيل رئيسي */
        .project-slider-container {
            position: relative;
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .project-inner-container {
            position: relative;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border: 1px solid #e1e8ed;
        }

        .project-slide {
            display: none;
        }

        .project-slide.active {
            display: block;
        }

        .project-slide .project-image {
            width: 100%;
            height: 120px;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .project-slide .project-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .project-nav-arrow {
            position: absolute;
            top: 15px;
            width: 25px;
            height: 25px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            z-index: 10;
            transition: all 0.3s;
        }

        .project-nav-arrow:hover {
            transform: scale(1.1);
        }

        .project-nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .project-nav-arrow.prev {
            right: 15px;
        }

        .project-nav-arrow.next {
            left: 15px;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="header">
        <div class="user-section">
            <div class="menu-icon">
                <i class="fas fa-bars"></i>
            </div>
            <div class="user-avatar">
                <img src="../get_me_app/images/Hussein Nihad.png" alt="المستخدم" onerror="this.style.display='none'">
            </div>
            <div class="user-name">حسين نهاد</div>
        </div>
        <div class="search-section">
            <input type="text" class="search-input" placeholder="ابحث عن خدمة أو منتج...">
            <button class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <div class="app-name">Get Me</div>
    </div>

    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item home-item">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </div>
            <div class="nav-item has-notification">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge">3</div>
            </div>
            <div class="nav-item">
                <i class="fas fa-comments"></i>
                <span>الرسائل</span>
            </div>
            <div class="nav-item active">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
            <div class="nav-item location-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>بغداد، العراق</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- مودال البروفايل الشخصي -->
        <div class="my-profile-modal">
            <div class="my-profile-content">
                <div class="my-profile-header">
                    <button class="close-profile-btn-simple" onclick="goBack()">
                        <i class="fas fa-arrow-right"></i>
                    </button>

                    <!-- معلومات البروفايل في الجزء الملون -->
                    <div class="profile-info-colored-full">
                        <!-- صورة البروفايل في الأعلى -->
                        <div class="profile-avatar-top-center">
                            <div class="profile-avatar-extra-large online">
                                <img src="../get_me_app/images/Hussein Nihad.png" alt="صورة حسين نهاد">
                            </div>
                        </div>

                        <h1 class="profile-name-white">حسين نهاد</h1>

                        <!-- النجوم - تقييم المستخدم -->
                        <div class="profile-rating-stars-colored">
                            <div class="stars-container-white">
                                <i class="fas fa-star star-yellow-large filled"></i>
                                <i class="fas fa-star star-yellow-large filled"></i>
                                <i class="fas fa-star star-yellow-large filled"></i>
                                <i class="fas fa-star star-yellow-large filled"></i>
                                <i class="fas fa-star star-yellow-large filled"></i>
                            </div>
                            <div class="rating-text-white">(5.0 من 5)</div>
                        </div>

                        <!-- الإحصائيات المتباعدة -->
                        <div class="profile-stats-colored">
                            <div class="stat-item-colored">
                                <span class="stat-number-white">1,234</span>
                                <span class="stat-label-white">متابع</span>
                            </div>
                            <div class="stat-item-colored">
                                <span class="stat-number-white">567</span>
                                <span class="stat-label-white">متابَع</span>
                            </div>
                            <div class="stat-item-colored">
                                <span class="stat-number-white">89</span>
                                <span class="stat-label-white">منشور</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط التنقل -->
                <div class="profile-nav">
                    <div class="profile-nav-item active" onclick="switchProfileTab('posts')">
                        <i class="fas fa-newspaper"></i> المنشورات
                    </div>
                    <div class="profile-nav-item" onclick="switchProfileTab('info')">
                        <i class="fas fa-info-circle"></i> المعلومات
                    </div>
                    <div class="profile-nav-item" onclick="switchProfileTab('photos')">
                        <i class="fas fa-images"></i> الصور
                    </div>
                    <div class="profile-nav-item" onclick="switchProfileTab('followers')">
                        <i class="fas fa-users"></i> المتابعين
                    </div>
                    <div class="profile-nav-item" onclick="switchProfileTab('settings')">
                        <i class="fas fa-cog"></i> الإعدادات
                    </div>
                </div>

                <!-- شريط المتابعين -->
                <div class="followers-bar">
                    <div class="followers-bar-content">
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/Hussein Nihad.png" alt="أحمد محمد">
                            </div>
                            <div class="follower-name-bar">أحمد محمد</div>
                        </div>
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/مطعم.jpg" alt="سارة علي">
                            </div>
                            <div class="follower-name-bar">سارة علي</div>
                        </div>
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/الطبية.jpg" alt="دكتور محمد">
                            </div>
                            <div class="follower-name-bar">دكتور محمد</div>
                        </div>
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/سيارات.jpg" alt="معرض السيارات">
                            </div>
                            <div class="follower-name-bar">معرض السيا...</div>
                        </div>
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/مطعم.jpg" alt="مطعم الأصالة">
                            </div>
                            <div class="follower-name-bar">مطعم الأصا...</div>
                        </div>
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/مصارف وبنوك.jpg" alt="بنك الأمان">
                            </div>
                            <div class="follower-name-bar">بنك الأمان</div>
                        </div>
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/زراعية.jpg" alt="مزرعة الخير">
                            </div>
                            <div class="follower-name-bar">مزرعة الخي...</div>
                        </div>
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/محلات.jpg" alt="متجر الإلكترونيات">
                            </div>
                            <div class="follower-name-bar">متجر الإلك...</div>
                        </div>
                        <div class="follower-item-bar">
                            <div class="follower-circle">
                                <img src="../get_me_app/images/Hussein Nihad.png" alt="علي حسن">
                            </div>
                            <div class="follower-name-bar">علي حسن</div>
                        </div>
                    </div>
                </div>

                <!-- شريط القصص -->
                <div class="stories-bar">
                    <div class="stories-bar-header">
                        <h3>
                            <i class="fas fa-plus-circle"></i>
                            قصصي
                        </h3>
                        <button class="add-story-btn" onclick="addStory()">
                            <i class="fas fa-plus"></i> إضافة قصة
                        </button>
                    </div>
                    <div class="stories-container">
                        <div class="story-item story-add" onclick="addStory()">
                            <div class="story-add">
                                <i class="fas fa-camera"></i>
                            </div>
                        </div>
                        <div class="story-item">
                            <img src="../get_me_app/images/Hussein Nihad.png" alt="قصة 1">
                        </div>
                        <div class="story-item">
                            <img src="../get_me_app/images/Hussein Nihad.png" alt="قصة 2">
                        </div>
                        <div class="story-item">
                            <img src="../get_me_app/images/Hussein Nihad.png" alt="قصة 3">
                        </div>
                    </div>
                </div>

                <!-- محتوى البروفايل -->
                <div class="profile-content-area">
                    <!-- الشريط الجانبي -->
                    <div class="profile-sidebar">
                        <!-- معلومات العمل المفصلة -->
                        <div class="work-details-card">
                            <div class="card-title">
                                <i class="fas fa-briefcase"></i>
                                معلومات العمل
                            </div>
                            <div class="work-detail-item">
                                <i class="fas fa-user-tie work-detail-icon"></i>
                                <div class="work-detail-content">
                                    <div class="work-detail-title">التخصص</div>
                                    <div class="work-detail-value">خبير عقاري ومستشار استثماري</div>
                                </div>
                            </div>
                            <div class="work-detail-item">
                                <i class="fas fa-clock work-detail-icon"></i>
                                <div class="work-detail-content">
                                    <div class="work-detail-title">سنوات الخبرة</div>
                                    <div class="work-detail-value">8 سنوات</div>
                                </div>
                            </div>
                            <div class="work-detail-item">
                                <i class="fas fa-tasks work-detail-icon"></i>
                                <div class="work-detail-content">
                                    <div class="work-detail-title">المشاريع المنجزة</div>
                                    <div class="work-detail-value">+150 مشروع</div>
                                </div>
                            </div>
                            <div class="work-detail-item">
                                <i class="fas fa-smile work-detail-icon"></i>
                                <div class="work-detail-content">
                                    <div class="work-detail-title">العملاء الراضون</div>
                                    <div class="work-detail-value">+300 عميل</div>
                                </div>
                            </div>
                            <div class="work-detail-item">
                                <i class="fas fa-star work-detail-icon"></i>
                                <div class="work-detail-content">
                                    <div class="work-detail-title">التقييم</div>
                                    <div class="work-detail-value">4.9/5 (245 تقييم)</div>
                                </div>
                            </div>
                            <div class="work-detail-item">
                                <i class="fas fa-map work-detail-icon"></i>
                                <div class="work-detail-content">
                                    <div class="work-detail-title">نطاق العمل</div>
                                    <div class="work-detail-value">بغداد والمحافظات</div>
                                </div>
                            </div>
                            <div class="work-detail-item">
                                <i class="fas fa-certificate work-detail-icon"></i>
                                <div class="work-detail-content">
                                    <div class="work-detail-title">الشهادات</div>
                                    <div class="work-detail-value">4 شهادات معتمدة</div>
                                </div>
                            </div>
                        </div>

                        <!-- المشاريع المنجزة -->
                        <div class="info-card">
                            <div class="card-title">
                                <i class="fas fa-building"></i>
                                المشاريع المنجزة
                            </div>
                            <div class="project-slider-container">
                                <button class="project-nav-arrow prev" id="projectPrevBtn" onclick="moveProjects('right')">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                                <button class="project-nav-arrow next" id="projectNextBtn" onclick="moveProjects('left')">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div class="project-inner-container">
                                    <div class="project-slider" id="projectSlider">
                                        <div class="project-slide active">
                                            <div class="project-image">
                                                <img src="../get_me_app/images/عقارات.jpg" alt="مجمع سكني فاخر في الجادرية">
                                            </div>
                                            <div class="project-title" style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">مجمع سكني فاخر في الجادرية</div>
                                            <div class="project-details">
                                                <div class="project-detail" style="font-size: 12px; margin-bottom: 3px;">
                                                    <span>📍 الجادرية، بغداد</span>
                                                </div>
                                                <div class="project-detail" style="font-size: 12px; margin-bottom: 3px;">
                                                    <span>⏱️ 8 أشهر</span>
                                                </div>
                                                <div class="project-detail" style="font-size: 12px;">
                                                    <span>💰 $120,000</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="project-slide">
                                            <div class="project-image">
                                                <img src="../get_me_app/images/عقارات.jpg" alt="برج تجاري في الكرادة">
                                            </div>
                                            <div class="project-title" style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">برج تجاري في الكرادة</div>
                                            <div class="project-details">
                                                <div class="project-detail" style="font-size: 12px; margin-bottom: 3px;">
                                                    <span>📍 الكرادة، بغداد</span>
                                                </div>
                                                <div class="project-detail" style="font-size: 12px; margin-bottom: 3px;">
                                                    <span>⏱️ 12 شهر</span>
                                                </div>
                                                <div class="project-detail" style="font-size: 12px;">
                                                    <span>💰 $250,000</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div style="text-align: center; margin-top: 10px;">
                                    <span style="color: #16CCC8; font-size: 12px; font-weight: 600;" id="projectCounter">1 / 8</span>
                                </div>
                            </div>
                        </div>

                        <!-- بطاقة الصور -->
                        <div class="photos-card">
                            <div class="card-title">
                                <i class="fas fa-images"></i>
                                الصور
                            </div>
                            <div class="photos-grid">
                                <div class="photo-item">
                                    <img src="../get_me_app/images/عقارات.jpg" alt="صورة">
                                </div>
                                <div class="photo-item">
                                    <img src="../get_me_app/images/عقارات.jpg" alt="صورة">
                                </div>
                                <div class="photo-item">
                                    <img src="../get_me_app/images/عقارات.jpg" alt="صورة">
                                </div>
                                <div class="photo-item">
                                    <img src="../get_me_app/images/عقارات.jpg" alt="صورة">
                                </div>
                                <div class="photo-item">
                                    <img src="../get_me_app/images/عقارات.jpg" alt="صورة">
                                </div>
                                <div class="photo-item">
                                    <img src="../get_me_app/images/عقارات.jpg" alt="صورة">
                                </div>
                            </div>
                            <div style="text-align: center; margin-top: 15px;">
                                <a href="#" style="color: #16CCC8; text-decoration: none; font-size: 14px; font-weight: 600;">عرض جميع الصور</a>
                            </div>
                        </div>

                        <!-- بطاقة المتابعين -->
                        <div class="followers-card">
                            <div class="card-title">
                                <i class="fas fa-users"></i>
                                المتابعين
                            </div>
                            <div class="followers-list">
                                <div class="follower-item">
                                    <div class="follower-avatar">
                                        <img src="../get_me_app/images/Hussein Nihad.png" alt="أحمد محمد">
                                    </div>
                                    <div class="follower-info">
                                        <div class="follower-name">أحمد محمد</div>
                                    </div>
                                </div>
                                <div class="follower-item">
                                    <div class="follower-avatar">
                                        <img src="../get_me_app/images/مطعم.jpg" alt="سارة علي">
                                    </div>
                                    <div class="follower-info">
                                        <div class="follower-name">سارة علي</div>
                                    </div>
                                </div>
                                <div class="follower-item">
                                    <div class="follower-avatar">
                                        <img src="../get_me_app/images/الطبية.jpg" alt="دكتور محمد">
                                    </div>
                                    <div class="follower-info">
                                        <div class="follower-name">دكتور محمد</div>
                                    </div>
                                </div>
                                <div class="follower-item">
                                    <div class="follower-avatar">
                                        <img src="../get_me_app/images/سيارات.jpg" alt="معرض السيارات">
                                    </div>
                                    <div class="follower-info">
                                        <div class="follower-name">معرض السيارات</div>
                                    </div>
                                </div>
                                <div class="follower-item">
                                    <div class="follower-avatar">
                                        <img src="../get_me_app/images/مطعم.jpg" alt="مطعم الأصالة">
                                    </div>
                                    <div class="follower-info">
                                        <div class="follower-name">مطعم الأصالة</div>
                                    </div>
                                </div>
                                <div class="follower-item">
                                    <div class="follower-avatar">
                                        <img src="../get_me_app/images/مصارف وبنوك.jpg" alt="بنك الأمان">
                                    </div>
                                    <div class="follower-info">
                                        <div class="follower-name">بنك الأمان</div>
                                    </div>
                                </div>
                            </div>
                            <div style="text-align: center; margin-top: 15px;">
                                <a href="#" style="color: #16CCC8; text-decoration: none; font-size: 14px; font-weight: 600;">عرض جميع المتابعين</a>
                            </div>
                        </div>


                    </div>

                    <!-- المحتوى الرئيسي -->
                    <div class="profile-main-content" id="profileMainContent">
                        <!-- نافذة إنشاء المنشور -->
                        <div class="create-post-section">
                            <div class="create-post-header">
                                <div class="create-post-avatar">
                                    <img src="../get_me_app/images/Hussein Nihad.png" alt="حسين نهاد">
                                </div>
                                <input type="text" class="create-post-input" placeholder="منشور جديد..." onclick="openCreatePost()">
                            </div>
                            <div class="create-post-actions">
                                <div class="create-action" onclick="addPhoto()">
                                    <i class="fas fa-camera"></i>
                                    <span>صورة/فيديو</span>
                                </div>
                                <div class="create-action" onclick="addFeeling()">
                                    <i class="fas fa-smile"></i>
                                    <span>شعور/نشاط</span>
                                </div>
                                <div class="create-action" onclick="addLocation()">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>موقع</span>
                                </div>
                            </div>
                        </div>

                        <!-- المنشورات -->
                        <div class="my-posts-container" id="postsContainer">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات البروفايل
        const profileData = {
            name: 'حسين نهاد',
            bio: 'خبير عقاري ومستشار استثماري',
            avatar: '../get_me_app/images/Hussein Nihad.png',
            followers: 1234,
            following: 567,
            posts: 89,
            rating: 5.0,
            work: 'خبير عقاري ومستشار استثماري',
            education: 'جامعة بغداد - هندسة حاسوب',
            location: 'بغداد، العراق',
            joinDate: 'يناير 2020',
            myPosts: [
                {
                    id: 1,
                    content: 'عقار فاخر في المنصور للبيع! شقة بمساحة 150 متر مربع، 3 غرف نوم، صالتين، مطبخ حديث، وإطلالة رائعة. السعر مناسب جداً!',
                    time: 'منذ ساعة',
                    likes: 89,
                    comments: 23,
                    shares: 12,
                    image: '../get_me_app/images/عقارات.jpg'
                },
                {
                    id: 2,
                    content: 'فيلا حديثة في الجادرية للإيجار 🏡 تصميم عصري، حديقة واسعة، موقف سيارات، وجميع الخدمات متوفرة. مناسبة للعائلات الكبيرة.',
                    time: 'منذ 3 ساعات',
                    likes: 156,
                    comments: 45,
                    shares: 28,
                    image: '../get_me_app/images/عقارات.jpg'
                },
                {
                    id: 3,
                    content: 'شقة أنيقة في الكرادة بموقع مميز! قريبة من جميع الخدمات والمرافق، مفروشة بالكامل، جاهزة للسكن الفوري. اتصلوا للاستفسار.',
                    time: 'منذ 5 ساعات',
                    likes: 234,
                    comments: 67,
                    shares: 34,
                    image: '../get_me_app/images/عقارات.jpg'
                },
                {
                    id: 4,
                    content: 'بيت تراثي أصيل في الأعظمية 🏛️ يحافظ على الطابع البغدادي التقليدي مع لمسات عصرية. مساحة واسعة وتصميم فريد.',
                    time: 'منذ 8 ساعات',
                    likes: 312,
                    comments: 89,
                    shares: 56,
                    image: '../get_me_app/images/عقارات.jpg'
                },
                {
                    id: 5,
                    content: 'شقة عائلية واسعة في الحارثية للبيع! مناسبة للعائلات الكبيرة، شرفات متعددة، إطلالة جميلة، وسعر تنافسي. فرصة ذهبية!',
                    time: 'منذ 12 ساعة',
                    likes: 178,
                    comments: 34,
                    shares: 19,
                    image: '../get_me_app/images/عقارات.jpg'
                },
                {
                    id: 6,
                    content: 'دوبلكس فاخر في اليرموك 🌟 طابقين بتشطيبات عالية الجودة، مساحات واسعة، تصميم حديث، وجميع وسائل الراحة متوفرة.',
                    time: 'منذ يوم',
                    likes: 445,
                    comments: 112,
                    shares: 78,
                    image: '../get_me_app/images/عقارات.jpg'
                },
                {
                    id: 7,
                    content: 'استوديو عصري في الزعفرانية للإيجار! مناسب للشباب والمهنيين، مجهز بالكامل، موقع ممتاز، وإيجار شهري مناسب.',
                    time: 'منذ يومين',
                    likes: 267,
                    comments: 56,
                    shares: 23,
                    image: '../get_me_app/images/عقارات.jpg'
                },
                {
                    id: 8,
                    content: 'قصر فخم في المنطقة الخضراء للبيع! 🏰 مساحات شاسعة، حدائق خلابة، مسبح، وجميع وسائل الراحة والرفاهية. عقار استثنائي!',
                    time: 'منذ 3 أيام',
                    likes: 892,
                    comments: 234,
                    shares: 156,
                    image: '../get_me_app/images/عقارات.jpg'
                }
            ],
            photos: [
                '../get_me_app/images/Hussein Nihad.png',
                '../get_me_app/images/Hussein Nihad.png',
                '../get_me_app/images/Hussein Nihad.png',
                '../get_me_app/images/Hussein Nihad.png',
                '../get_me_app/images/Hussein Nihad.png',
                '../get_me_app/images/Hussein Nihad.png'
            ],
            followers_list: [
                { name: 'أحمد محمد', status: 'نشط الآن', avatar: '../get_me_app/images/Hussein Nihad.png' },
                { name: 'سارة علي', status: 'نشطة منذ 5 دقائق', avatar: '../get_me_app/images/Hussein Nihad.png' },
                { name: 'دكتور محمد', status: 'نشط منذ ساعة', avatar: '../get_me_app/images/Hussein Nihad.png' },
                { name: 'معرض السيارات', status: 'نشط منذ 10 دقائق', avatar: '../get_me_app/images/Hussein Nihad.png' },
                { name: 'مطعم الأصالة', status: 'نشط منذ 30 دقيقة', avatar: '../get_me_app/images/Hussein Nihad.png' },
                { name: 'بنك الأمان', status: 'نشط منذ ساعتين', avatar: '../get_me_app/images/Hussein Nihad.png' },
                { name: 'مزرعة الخير', status: 'نشط منذ 3 ساعات', avatar: '../get_me_app/images/Hussein Nihad.png' },
                { name: 'متجر الإلكترونيات', status: 'نشط منذ 4 ساعات', avatar: '../get_me_app/images/Hussein Nihad.png' },
                { name: 'علي حسن', status: 'نشط منذ 5 ساعات', avatar: '../get_me_app/images/Hussein Nihad.png' }
            ],
            projects: [
                {
                    id: 1,
                    title: 'مجمع سكني فاخر في الجادرية',
                    location: 'الجادرية، بغداد',
                    duration: '8 أشهر',
                    value: '$120,000',
                    image: '../get_me_app/images/Hussein Nihad.png'
                },
                {
                    id: 2,
                    title: 'برج تجاري في الكرادة',
                    location: 'الكرادة، بغداد',
                    duration: '12 شهر',
                    value: '$250,000',
                    image: '../get_me_app/images/Hussein Nihad.png'
                },
                {
                    id: 3,
                    title: 'مجمع سكني في المنصور',
                    location: 'المنصور، بغداد',
                    duration: '6 أشهر',
                    value: '$180,000',
                    image: '../get_me_app/images/Hussein Nihad.png'
                }
            ],
            certificates: [
                {
                    id: 1,
                    title: 'شهادة الخبرة العقارية',
                    issuer: 'نقابة المهندسين العراقية',
                    icon: 'fas fa-home'
                },
                {
                    id: 2,
                    title: 'شهادة الاستشارة الاستثمارية',
                    issuer: 'المعهد العراقي للاستثمار',
                    icon: 'fas fa-chart-line'
                },
                {
                    id: 3,
                    title: 'شهادة إدارة المشاريع',
                    issuer: 'جامعة بغداد',
                    icon: 'fas fa-tasks'
                },
                {
                    id: 4,
                    title: 'شهادة التقييم العقاري',
                    issuer: 'الجمعية العراقية للمقيمين',
                    icon: 'fas fa-calculator'
                }
            ]
        };

        // العودة للصفحة السابقة
        function goBack() {
            window.history.back();
        }

        // تبديل التبويبات
        function switchProfileTab(tab) {
            // تحديث التبويبات النشطة
            document.querySelectorAll('.profile-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            const mainContent = document.getElementById('profileMainContent');

            switch(tab) {
                case 'posts':
                    mainContent.innerHTML = renderPosts();
                    break;
                case 'info':
                    mainContent.innerHTML = renderInfo();
                    break;
                case 'photos':
                    mainContent.innerHTML = renderPhotos();
                    break;
                case 'followers':
                    mainContent.innerHTML = renderFollowers();
                    break;
                case 'settings':
                    mainContent.innerHTML = renderSettings();
                    break;
            }
        }

        // عرض المنشورات
        function renderPosts() {
            const container = document.getElementById('postsContainer');
            container.innerHTML = profileData.myPosts.map(post => `
                <div class="post-card">
                    <div class="post-header">
                        <div class="post-avatar">
                            <img src="${profileData.avatar}" alt="${profileData.name}">
                        </div>
                        <div class="post-info">
                            <h4>${profileData.name}</h4>
                            <div class="post-time">${post.time}</div>
                        </div>
                    </div>
                    <div class="post-content">${post.content}</div>
                    ${post.image ? `
                        <div class="post-image" style="margin: 15px 0;">
                            <img src="${post.image}" alt="صورة المنشور" style="width: 100%; height: 300px; object-fit: cover; border-radius: 10px;">
                        </div>
                    ` : ''}
                    <div class="post-stats" style="display: flex; justify-content: space-around; padding: 10px 0; border-top: 1px solid #f0f0f0; border-bottom: 1px solid #f0f0f0; margin: 15px 0; font-size: 14px; color: #666;">
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-heart" style="color: #16CCC8;"></i>
                            <span>${post.likes}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-comment" style="color: #16CCC8;"></i>
                            <span>${post.comments}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-share" style="color: #16CCC8;"></i>
                            <span>${post.shares}</span>
                        </div>
                    </div>
                    <div class="post-actions">
                        <div class="post-action" onclick="likePost(${post.id})">
                            <i class="fas fa-heart"></i>
                            <span>إعجاب</span>
                        </div>
                        <div class="post-action" onclick="commentPost(${post.id})">
                            <i class="fas fa-comment"></i>
                            <span>تعليق</span>
                        </div>
                        <div class="post-action" onclick="sharePost(${post.id})">
                            <i class="fas fa-share"></i>
                            <span>مشاركة</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // عرض المعلومات
        function renderInfo() {
            const container = document.getElementById('postsContainer');
            container.innerHTML = `
                <!-- الشهادات -->
                <div class="info-card">
                    <div class="card-title">
                        <i class="fas fa-certificate"></i>
                        الشهادات المعتمدة
                    </div>
                    <div class="certificates-grid">
                        ${profileData.certificates.map(cert => `
                            <div class="certificate-card">
                                <div class="certificate-icon">
                                    <i class="${cert.icon}"></i>
                                </div>
                                <div class="certificate-title">${cert.title}</div>
                                <div class="certificate-issuer">${cert.issuer}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- المشاريع المنجزة -->
                <div class="info-card">
                    <div class="card-title">
                        <i class="fas fa-building"></i>
                        المشاريع المنجزة
                    </div>
                    <div class="projects-grid">
                        ${profileData.projects.map(project => `
                            <div class="project-card">
                                <div class="project-image">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="project-content">
                                    <div class="project-title">${project.title}</div>
                                    <div class="project-details">
                                        <div class="project-detail">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>${project.location}</span>
                                        </div>
                                        <div class="project-detail">
                                            <i class="fas fa-clock"></i>
                                            <span>${project.duration}</span>
                                        </div>
                                        <div class="project-detail">
                                            <i class="fas fa-dollar-sign"></i>
                                            <span>${project.value}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- المعلومات الشخصية -->
                <div class="info-card">
                    <div class="card-title">
                        <i class="fas fa-user"></i>
                        المعلومات الشخصية التفصيلية
                    </div>
                    <div class="info-item">
                        <i class="fas fa-user"></i>
                        <span><strong>الاسم:</strong> ${profileData.name}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-briefcase"></i>
                        <span><strong>العمل:</strong> ${profileData.work}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-graduation-cap"></i>
                        <span><strong>التعليم:</strong> ${profileData.education}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span><strong>الموقع:</strong> ${profileData.location}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span><strong>تاريخ الانضمام:</strong> ${profileData.joinDate}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-star"></i>
                        <span><strong>التقييم:</strong> ${profileData.rating}/5</span>
                    </div>
                </div>
            `;
        }

        // عرض الصور
        function renderPhotos() {
            return `
                <div class="photos-card">
                    <div class="card-title">
                        <i class="fas fa-images"></i>
                        جميع الصور (${profileData.photos.length})
                    </div>
                    <div class="photos-grid" style="grid-template-columns: repeat(4, 1fr); gap: 15px;">
                        ${profileData.photos.map((photo, index) => `
                            <div class="photo-item" onclick="viewPhoto(${index})">
                                <img src="${photo}" alt="صورة ${index + 1}">
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // عرض المتابعين
        function renderFollowers() {
            return `
                <div class="followers-card">
                    <div class="card-title">
                        <i class="fas fa-users"></i>
                        المتابعين (${profileData.followers_list.length})
                    </div>
                    <div class="followers-list">
                        ${profileData.followers_list.map(follower => `
                            <div class="follower-item">
                                <div class="follower-avatar">
                                    <img src="${follower.avatar}" alt="${follower.name}">
                                </div>
                                <div class="follower-info">
                                    <div class="follower-name">${follower.name}</div>
                                    <div class="follower-status">${follower.status}</div>
                                </div>
                                <button class="follow-btn" onclick="toggleFollow('${follower.name}')">متابعة</button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // عرض الإعدادات
        function renderSettings() {
            return `
                <div class="info-card">
                    <div class="card-title">
                        <i class="fas fa-cog"></i>
                        إعدادات الحساب
                    </div>
                    <div class="info-item" onclick="editProfile()">
                        <i class="fas fa-edit"></i>
                        <span>تعديل الملف الشخصي</span>
                    </div>
                    <div class="info-item" onclick="changePassword()">
                        <i class="fas fa-lock"></i>
                        <span>تغيير كلمة المرور</span>
                    </div>
                    <div class="info-item" onclick="privacySettings()">
                        <i class="fas fa-shield-alt"></i>
                        <span>إعدادات الخصوصية</span>
                    </div>
                    <div class="info-item" onclick="notificationSettings()">
                        <i class="fas fa-bell"></i>
                        <span>إعدادات الإشعارات</span>
                    </div>
                    <div class="info-item" onclick="accountSettings()">
                        <i class="fas fa-user-cog"></i>
                        <span>إعدادات الحساب</span>
                    </div>
                    <div class="info-item" onclick="logout()" style="color: #e74c3c;">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </div>
                </div>
            `;
        }

        // دوال التفاعل
        function likePost(postId) {
            const post = profileData.myPosts.find(p => p.id === postId);
            if (post) {
                post.likes += 1;
                showNotification('تم الإعجاب بالمنشور! ❤️');
                switchProfileTab('posts');
            }
        }

        function commentPost(postId) {
            const comment = prompt('اكتب تعليقك:');
            if (comment) {
                const post = profileData.myPosts.find(p => p.id === postId);
                if (post) {
                    post.comments += 1;
                    showNotification('تم إضافة تعليقك! 💬');
                    switchProfileTab('posts');
                }
            }
        }

        function sharePost(postId) {
            const post = profileData.myPosts.find(p => p.id === postId);
            if (post) {
                post.shares += 1;
                showNotification('تم مشاركة المنشور! 📤');
                switchProfileTab('posts');
            }
        }

        function viewPhoto(index) {
            alert(`عرض الصورة رقم ${index + 1}`);
        }

        function toggleFollow(name) {
            showNotification(`تم تغيير حالة المتابعة لـ ${name}`);
        }

        // دوال الإعدادات
        function editProfile() {
            alert('تعديل الملف الشخصي\n\nسيتم فتح نافذة التعديل...');
        }

        function changePassword() {
            alert('تغيير كلمة المرور\n\nسيتم فتح نافذة تغيير كلمة المرور...');
        }

        function privacySettings() {
            alert('إعدادات الخصوصية\n\nسيتم فتح إعدادات الخصوصية...');
        }

        function notificationSettings() {
            alert('إعدادات الإشعارات\n\nسيتم فتح إعدادات الإشعارات...');
        }

        function accountSettings() {
            alert('إعدادات الحساب\n\nسيتم فتح إعدادات الحساب...');
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                alert('تم تسجيل الخروج بنجاح!');
                window.location.href = 'Home.html';
            }
        }

        // دوال إنشاء المحتوى
        function openCreatePost() {
            const content = prompt('اكتب منشورك الجديد:');
            if (content && content.trim()) {
                const newPost = {
                    id: profileData.myPosts.length + 1,
                    content: content.trim(),
                    time: 'الآن',
                    likes: 0,
                    comments: 0,
                    shares: 0,
                    image: null
                };
                profileData.myPosts.unshift(newPost);
                showNotification('تم نشر المنشور بنجاح! 📝');
                renderPosts();
            }
        }

        function addPhoto() {
            alert('إضافة صورة/فيديو\n\nسيتم فتح معرض الصور...');
        }

        function addFeeling() {
            const feelings = ['سعيد 😊', 'متحمس 🎉', 'ممتن 🙏', 'فخور 💪', 'مبدع 🎨'];
            const feeling = prompt('اختر شعورك:\n\n' + feelings.map((f, i) => `${i+1}. ${f}`).join('\n'));
            if (feeling) {
                showNotification('تم إضافة الشعور! 😊');
            }
        }

        function addLocation() {
            const location = prompt('أضف موقعك:');
            if (location && location.trim()) {
                showNotification(`تم إضافة الموقع: ${location} 📍`);
            }
        }

        function addStory() {
            alert('إضافة قصة جديدة\n\nسيتم فتح الكاميرا لالتقاط صورة أو فيديو...');
        }

        // متغيرات المشاريع
        let currentProjectIndex = 0;
        const totalProjects = 8;
        let projectAutoSlideInterval;

        // تحريك المشاريع
        function moveProjects(direction) {
            // إخفاء المشروع الحالي
            const currentSlide = document.querySelector('.project-slide.active');
            if (currentSlide) {
                currentSlide.classList.remove('active');
            }

            if (direction === 'left' && currentProjectIndex < totalProjects - 1) {
                currentProjectIndex++;
            } else if (direction === 'right' && currentProjectIndex > 0) {
                currentProjectIndex--;
            } else if (direction === 'auto') {
                // التنقل التلقائي
                currentProjectIndex = (currentProjectIndex + 1) % 2; // التنقل بين المشروعين المتاحين
            }

            // إظهار المشروع الجديد
            const slides = document.querySelectorAll('.project-slide');
            if (slides[currentProjectIndex]) {
                slides[currentProjectIndex].classList.add('active');
            }

            // تحديث العداد
            document.getElementById('projectCounter').textContent = `${currentProjectIndex + 1} / ${totalProjects}`;

            // تحديث أزرار التنقل
            updateProjectButtons();
        }

        // تحديث أزرار التنقل للمشاريع
        function updateProjectButtons() {
            const prevBtn = document.getElementById('projectPrevBtn');
            const nextBtn = document.getElementById('projectNextBtn');

            prevBtn.disabled = currentProjectIndex === 0;
            nextBtn.disabled = currentProjectIndex >= 1; // لدينا مشروعان فقط حالياً
        }

        // بدء التنقل التلقائي
        function startProjectAutoSlide() {
            projectAutoSlideInterval = setInterval(() => {
                moveProjects('auto');
            }, 60000); // كل دقيقة (60000 مللي ثانية)
        }

        // إيقاف التنقل التلقائي
        function stopProjectAutoSlide() {
            if (projectAutoSlideInterval) {
                clearInterval(projectAutoSlideInterval);
            }
        }

        // دالة إظهار الإشعارات
        function showNotification(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #16CCC8, #227FCC);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                font-weight: 500;
                animation: slideIn 0.3s ease-out;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // التنقل في شريط التنقل
        function navigateToHome() {
            window.location.href = 'Home.html';
        }

        function navigateToNotifications() {
            alert('الانتقال إلى صفحة الإشعارات...');
        }

        function navigateToMessages() {
            alert('الانتقال إلى صفحة الرسائل...');
        }

        function changeLocation() {
            alert('تغيير الموقع...');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // عرض المنشورات عند تحميل الصفحة
            renderPosts();

            // تهيئة أزرار المشاريع
            updateProjectButtons();

            // بدء التنقل التلقائي للمشاريع
            startProjectAutoSlide();

            // إضافة أحداث التنقل
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, index) => {
                item.addEventListener('click', function() {
                    const navText = this.querySelector('span').textContent;
                    switch(navText) {
                        case 'الرئيسية':
                            navigateToHome();
                            break;
                        case 'الإشعارات':
                            navigateToNotifications();
                            break;
                        case 'الرسائل':
                            navigateToMessages();
                            break;
                        case 'الملف الشخصي':
                            // نحن بالفعل في صفحة البروفايل
                            break;
                        case 'بغداد، العراق':
                            changeLocation();
                            break;
                    }
                });
            });

            // إضافة حدث البحث
            document.querySelector('.search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const searchTerm = this.value.trim();
                    if (searchTerm) {
                        alert('البحث عن: ' + searchTerm);
                    }
                }
            });

            // إضافة حدث زر البحث
            document.querySelector('.search-btn').addEventListener('click', function() {
                const searchTerm = document.querySelector('.search-input').value.trim();
                if (searchTerm) {
                    alert('البحث عن: ' + searchTerm);
                }
            });

            // إضافة حدث القائمة
            document.querySelector('.menu-icon').addEventListener('click', function() {
                alert('فتح القائمة الرئيسية...');
            });

            // إيقاف التنقل التلقائي عند مغادرة الصفحة
            window.addEventListener('beforeunload', function() {
                stopProjectAutoSlide();
            });

            console.log('تم تحميل صفحة البروفايل بنجاح! 🎉');
        });

        // إضافة CSS للرسوم المتحركة
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
