<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الخدمات - Get Me</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* منع التمرير الأفقي غير المرغوب فيه */
        html, body {
            overflow-x: hidden;
            max-width: 100%;
        }

        /* منع التمرير الأفقي للحاويات الرئيسية */
        .main-content {
            overflow-x: hidden;
            max-width: 100%;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
            color: #333;
        }

        /* الشريط العلوي */
        .header {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 1000 !important;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100% !important;
        }

        .app-name {
            font-size: 40px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
            max-width: 600px;
            margin: 0 30px;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 12px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.15);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            outline: none;
            color: white;
            transition: all 0.3s ease;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
            font-weight: 500;
        }

        .search-input:focus {
            outline: none;
        }

        .search-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: white;
            font-size: 24px;
            padding: 10px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            position: relative;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        /* شريط التنقل */
        .nav-bar {
            background: white;
            position: fixed !important;
            top: 70px !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 999 !important;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            width: 100% !important;
        }

        .nav-items {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 25px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.3s;
            color: #495057;
            font-weight: 500;
            position: relative;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            color: #16CCC8;
            transform: translateY(-2px);
        }

        .nav-item:hover i {
            transform: scale(1.1);
            color: #16CCC8;
        }

        .nav-item i {
            font-size: 24px;
            transition: all 0.3s ease;
        }

        .nav-item.home-item {
            margin-left: 80px;
        }

        .nav-item.home-item i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item.home-item:hover i,
        .nav-item.home-item.active i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item.location-item {
            margin-right: auto;
            background: rgba(22, 204, 200, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(22, 204, 200, 0.3);
        }

        .nav-item.location-item:hover {
            background: rgba(22, 204, 200, 0.2);
            border-color: rgba(22, 204, 200, 0.5);
        }

        .nav-item.has-notification i {
            color: #ff0000 !important;
            -webkit-text-fill-color: #ff0000 !important;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 20px;
        }

        /* إخفاء شريط التمرير */
        #sellersStoriesContainer::-webkit-scrollbar,
        #liveStreamsContainer::-webkit-scrollbar,
        #premiumOffersContainer::-webkit-scrollbar {
            display: none;
        }

        /* تحسين تأثيرات القصص */
        .sellers-stories h3:hover {
            color: #16CCC8 !important;
            transition: all 0.3s ease;
        }

        /* تأثيرات الأسهم */
        #storiesLeftArrow:hover, #storiesRightArrow:hover {
            background: rgba(22, 204, 200, 1) !important;
            transform: translateY(-50%) scale(1.1) !important;
            box-shadow: 0 4px 12px rgba(22, 204, 200, 0.4) !important;
        }

        /* تأثيرات أسهم البث المباشر */
        #liveLeftArrow:hover, #liveRightArrow:hover {
            background: rgba(231, 76, 60, 1) !important;
            transform: translateY(-50%) scale(1.1) !important;
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.6) !important;
        }

        /* تأثير النبض لعلامة LIVE */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* تأثير الشيمر للعروض المميزة */
        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* منشورات البروفايل */
        .my-posts-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .post-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .post-card:hover {
            border: 2px solid #16CCC8;
            box-shadow: 0 8px 25px rgba(22, 204, 200, 0.15);
        }

        .post-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .post-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
        }

        .post-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .post-info h4 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .post-time {
            color: #666;
            font-size: 12px;
        }

        .post-content {
            color: #333;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .post-actions {
            display: flex;
            justify-content: space-around;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .post-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            font-size: 14px;
        }

        .post-action:hover {
            background: rgba(22, 204, 200, 0.1);
            color: #16CCC8;
        }

        .post-action i {
            font-size: 16px;
        }



        /* شريط القصص */
        .stories-section {
            margin-bottom: 20px;
            position: relative;
        }

        .stories-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }

        .stories-wrapper {
            display: flex;
            gap: 15px;
            transition: transform 1s ease;
            width: fit-content;
        }

        .story-card {
            width: 120px;
            height: 180px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            cursor: pointer;
            background: white;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            position: relative;
        }

        .story-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .story-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            color: white;
            font-size: 24px;
        }

        .story-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .story-name {
            height: 60px;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: 600;
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
        }

        /* أسهم التنقل */
        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            z-index: 10;
            transition: all 0.3s;
        }

        .nav-arrow:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%);
        }

        .nav-arrow.prev {
            right: 10px;
        }

        .nav-arrow.next {
            left: 10px;
        }
    </style>
</head>
<body dir="rtl">
    <!-- الشريط العلوي -->
    <div class="header">
        <div class="user-section">
            <div class="menu-icon">
                <i class="fas fa-bars"></i>
            </div>
            <div class="user-avatar">
                <img src="../get_me_app/images/Hussein Nihad.png" alt="المستخدم" onerror="this.style.display='none'">
            </div>
            <div class="user-name">حسين نهاد</div>
        </div>
        <div class="search-section">
            <input type="text" class="search-input" placeholder="ابحث عن خدمة أو منتج...">
            <button class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <div class="app-name">Get Me</div>
    </div>

    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item home-item active">
              <a href="Home.html">
  <i class="fas fa-home"></i>
</a>

                <span>الرئيسية</span>
            </div>
            <div class="nav-item has-notification">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge">3</div>
            </div>
            <div class="nav-item">
                <i class="fas fa-comments"></i>
                <span>الرسائل</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
            <div class="nav-item location-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>بغداد، العراق</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- عنوان القصص -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-circle" style="color: #16CCC8; margin-left: 8px;"></i>
                قصص البائعين
            </h3>
        </div>

        <!-- شريط القصص للبائعين -->
        <div class="sellers-stories" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <!-- زر إنشاء قصة داخل الشريط من اليمين -->
            <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;">
                <button onclick="createNewStory()" style="
                    background: linear-gradient(45deg, #16CCC8, #227FCC);
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(22, 204, 200, 0.3);
                " onmouseover="
                    this.style.transform='translateY(-2px)';
                    this.style.boxShadow='0 4px 12px rgba(22, 204, 200, 0.5)';
                " onmouseout="
                    this.style.transform='translateY(0)';
                    this.style.boxShadow='0 2px 8px rgba(22, 204, 200, 0.3)';
                ">
                    <i class="fas fa-plus" style="margin-left: 5px;"></i>
                    إنشاء قصة جديدة
                </button>
            </div>
            <div style="position: relative;">
                <!-- سهم التمرير الأيسر -->
                <button id="storiesLeftArrow" onclick="scrollStories('left')" style="
                    position: absolute;
                    left: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="storiesRightArrow" onclick="scrollStories('right')" style="
                    position: absolute;
                    right: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="sellersStoriesContainer" style="
                    display: flex;
                    gap: 15px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 10px 15px;
                    height: 95px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                    align-items: center;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- عنوان البث المباشر -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-video" style="color: #e74c3c; margin-left: 8px;"></i>
                البث المباشر
            </h3>
        </div>

        <!-- شريط البث المباشر -->
        <div class="live-streams" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <!-- زر بدء بث مباشر داخل الشريط من اليمين -->
            <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;">
                <button onclick="startLiveStream()" style="
                    background: linear-gradient(45deg, #8e44ad, #e74c3c, #f39c12);
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
                " onmouseover="
                    this.style.transform='translateY(-2px)';
                    this.style.boxShadow='0 4px 12px rgba(231, 76, 60, 0.5)';
                " onmouseout="
                    this.style.transform='translateY(0)';
                    this.style.boxShadow='0 2px 8px rgba(231, 76, 60, 0.3)';
                ">
                    <i class="fas fa-video" style="margin-left: 5px;"></i>
                    بدء بث مباشر
                </button>
            </div>
            <div style="position: relative;">
                <!-- سهم التمرير الأيسر -->
                <button id="liveLeftArrow" onclick="scrollLiveStreams('left')" style="
                    position: absolute;
                    left: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(231, 76, 60, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="liveRightArrow" onclick="scrollLiveStreams('right')" style="
                    position: absolute;
                    right: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(231, 76, 60, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="liveStreamsContainer" style="
                    display: flex;
                    gap: 15px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 10px 15px;
                    height: 95px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                    align-items: center;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- عنوان العروض المميزة -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-star" style="color: #f39c12; margin-left: 8px;"></i>
                العروض المميزة - عقارات
            </h3>
        </div>

        <!-- شريط العروض المميزة -->
        <div class="premium-offers-section" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <div class="premium-offers-container" style="
                position: relative;
                overflow: hidden;
                padding: 10px 0;
            ">
                <!-- سهم التمرير الأيسر -->
                <button id="premiumLeftArrow" onclick="scrollPremiumOffers('left')" style="
                    position: absolute;
                    left: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 45px;
                    height: 45px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="premiumRightArrow" onclick="scrollPremiumOffers('right')" style="
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 45px;
                    height: 45px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="premiumOffersContainer" style="
                    display: flex;
                    gap: 20px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 0 60px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- قسم المحتوى السفلي -->
        <div style="display: flex; gap: 25px; align-items: start; flex-direction: row-reverse;">


            <!-- عمود الخدمات العقارية: تحليل السوق العقاري ثم أسعار العقارات ثم المشاريع الحكومية (الجهة اليسرى) -->
            <div style="display: flex; flex-direction: column; align-items: flex-start; width: 480px; max-width: 100%; margin-left: 0; margin-right: 0; position: relative; left: 0;">
                <!-- مستطيل تحليل السوق العقاري كامل (مطابق للنسخة الأصلية) -->
                <div id="marketAnalysisWidget" style="background: white; border-radius: 20px; padding: 36px; margin-bottom: 24px; box-shadow: 0 6px 20px rgba(0,0,0,0.13); border: 1px solid #e4e6ea; width: 100%;">
                    <div style="text-align: center; margin-bottom: 18px; font-size: 15px;">
                        <h4 style="margin: 0; color: #333; font-size: 22px; font-weight: bold;">
                            <i class="fas fa-chart-line" style="color: #16CCC8; margin-left: 10px; font-size: 24px;"></i>
                            تحليل السوق العقاري
                        </h4>
                    </div>
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="display: inline-flex; background: #f8f9fa; border-radius: 25px; padding: 5px; border: 1px solid #e4e6ea; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <button onclick="changeChartType('weekly')" id="weeklyBtn" style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 12px 20px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px; transition: all 0.3s ease; box-shadow: 0 2px 10px rgba(22, 204, 200, 0.3);">أسبوعي</button>
                            <button onclick="changeChartType('monthly')" id="monthlyBtn" style="background: transparent; border: none; color: #666; padding: 12px 20px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px; transition: all 0.3s ease;">شهري</button>
                            <button onclick="changeChartType('yearly')" id="yearlyBtn" style="background: transparent; border: none; color: #666; padding: 12px 20px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px; transition: all 0.3s ease;">سنوي</button>
                        </div>
                    </div>
                    <div style="background: #fafbfc; border: 1px solid #e4e6ea; border-radius: 15px; padding: 25px; margin-bottom: 25px;">
                        <div style="color: #333; font-size: 16px; font-weight: bold; margin-bottom: 20px; text-align: center;" id="chartTitle">📊 الأداء الأسبوعي</div>
                        <div id="chartContainer" style="height: 150px; position: relative;">
                            <!-- سيتم ملؤه بـ JavaScript -->
                        </div>
                        <div id="chartLabels" style="display: flex; justify-content: space-between; margin-top: 15px; font-size: 12px; color: #666;">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, #16CCC8, #227FCC); color: white; padding: 20px; border-radius: 15px; text-align: center; margin-bottom: 20px;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 5px;" id="mainIndicator">+12%</div>
                        <div style="font-size: 16px; opacity: 0.9;" id="indicatorPeriod">هذا الأسبوع</div>
                        <div style="font-size: 14px; opacity: 0.8; margin-top: 5px;">📈 نمو مستمر في السوق</div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                        <div style="background: #e8f5e8; border: 1px solid #c3e6c3; padding: 20px; border-radius: 12px; text-align: center; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(39, 174, 96, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                            <div style="color: #27ae60; font-size: 24px; margin-bottom: 8px;"><i class="fas fa-home"></i></div>
                            <div style="color: #27ae60; font-weight: bold; font-size: 18px; margin-bottom: 5px;">+8%</div>
                            <div style="color: #666; font-size: 12px;">الشقق</div>
                        </div>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 12px; text-align: center; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(243, 156, 18, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                            <div style="color: #f39c12; font-size: 24px; margin-bottom: 8px;"><i class="fas fa-map-marked-alt"></i></div>
                            <div style="color: #f39c12; font-weight: bold; font-size: 18px; margin-bottom: 5px;">+15%</div>
                            <div style="color: #666; font-size: 12px;">الأراضي</div>
                        </div>
                        <div style="background: #ffeaa7; border: 1px solid #fdcb6e; padding: 20px; border-radius: 12px; text-align: center; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(230, 126, 34, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                            <div style="color: #e67e22; font-size: 24px; margin-bottom: 8px;"><i class="fas fa-building"></i></div>
                            <div style="color: #e67e22; font-weight: bold; font-size: 18px; margin-bottom: 5px;">+5%</div>
                            <div style="color: #666; font-size: 12px;">التجاري</div>
                        </div>
                    </div>
                </div>
                <!-- مستطيل أسعار العقارات - هذا الأسبوع كامل (مطابق للنسخة الأصلية) -->
                <div style="background: white; border-radius: 15px; padding: 28px; margin-bottom: 24px; box-shadow: 0 6px 20px rgba(0,0,0,0.13); width: 100%;">
                    <h4 style="margin: 0 0 15px 0; color: #333; font-size: 16px;">
                        <i class="fas fa-map-marked-alt" style="color: #e74c3c; margin-left: 8px;"></i>
                        أسعار العقارات - هذا الأسبوع
                    </h4>
                    <div style="space-y: 10px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; color: #333; font-size: 14px;">بغداد</div>
                                <div style="color: #666; font-size: 12px;">متوسط السعر</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-weight: bold; color: #16CCC8; font-size: 14px;">180,000$</div>
                                <div style="color: #27ae60; font-size: 12px;"><i class="fas fa-arrow-up"></i> 8%</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; color: #333; font-size: 14px;">البصرة</div>
                                <div style="color: #666; font-size: 12px;">متوسط السعر</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-weight: bold; color: #16CCC8; font-size: 14px;">120,000$</div>
                                <div style="color: #27ae60; font-size: 12px;"><i class="fas fa-arrow-up"></i> 12%</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; color: #333; font-size: 14px;">أربيل</div>
                                <div style="color: #666; font-size: 12px;">متوسط السعر</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-weight: bold; color: #16CCC8; font-size: 14px;">150,000$</div>
                                <div style="color: #27ae60; font-size: 12px;"><i class="fas fa-arrow-up"></i> 5%</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; color: #333; font-size: 14px;">النجف</div>
                                <div style="color: #666; font-size: 12px;">متوسط السعر</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-weight: bold; color: #16CCC8; font-size: 14px;">95,000$</div>
                                <div style="color: #27ae60; font-size: 12px;"><i class="fas fa-arrow-up"></i> 15%</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; color: #333; font-size: 14px;">كربلاء</div>
                                <div style="color: #666; font-size: 12px;">متوسط السعر</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-weight: bold; color: #16CCC8; font-size: 14px;">110,000$</div>
                                <div style="color: #27ae60; font-size: 12px;"><i class="fas fa-arrow-up"></i> 7%</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; color: #333; font-size: 14px;">الموصل</div>
                                <div style="color: #666; font-size: 12px;">متوسط السعر</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-weight: bold; color: #16CCC8; font-size: 14px;">85,000$</div>
                                <div style="color: #e74c3c; font-size: 12px;"><i class="fas fa-arrow-down"></i> 3%</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; color: #333; font-size: 14px;">السليمانية</div>
                                <div style="color: #666; font-size: 12px;">متوسط السعر</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-weight: bold; color: #16CCC8; font-size: 14px;">130,000$</div>
                                <div style="color: #27ae60; font-size: 12px;"><i class="fas fa-arrow-up"></i> 9%</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; color: #333; font-size: 14px;">ديالى</div>
                                <div style="color: #666; font-size: 12px;">متوسط السعر</div>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-weight: bold; color: #16CCC8; font-size: 14px;">75,000$</div>
                                <div style="color: #27ae60; font-size: 12px;"><i class="fas fa-arrow-up"></i> 4%</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- مستطيل المشاريع الحكومية منفصل -->
                <div style="background: white; border-radius: 15px; padding: 28px; box-shadow: 0 6px 20px rgba(0,0,0,0.13); width: 100%;">
                    <h4 style="margin: 0 0 15px 0; color: #333; font-size: 16px;">
                        <i class="fas fa-building" style="color: #f39c12; margin-left: 8px;"></i>
                        المشاريع الحكومية
                    </h4>
                    <div style="margin-bottom: 15px;">
                        <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 15px; border-radius: 10px; text-align: center; margin-bottom: 15px;">
                            <div style="font-size: 18px; font-weight: bold; margin-bottom: 5px;">
                                <i class="fas fa-handshake"></i>
                                فرص استثمارية
                            </div>
                            <div style="font-size: 14px; opacity: 0.9;">مشاريع تبحث عن مستثمرين</div>
                        </div>
                    </div>
                    <div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 15px; margin-bottom: 15px; background: #fafafa;">
                            <h5 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">مشروع المدينة الذكية - بغداد</h5>
                            <p style="margin: 0 0 10px 0; color: #666; font-size: 12px; line-height: 1.4;">مشروع سكني متكامل يحتاج مستثمرين ومقاولين للبناء والتطوير</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 13px;">500 مليون دولار</div>
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 6px 12px; border-radius: 15px; cursor: pointer; font-size: 11px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 15px; margin-bottom: 15px; background: #fafafa;">
                            <h5 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">مجمع تجاري - البصرة</h5>
                            <p style="margin: 0 0 10px 0; color: #666; font-size: 12px; line-height: 1.4;">مجمع تجاري كبير في منطقة استراتيجية يبحث عن شركاء استثماريين</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 13px;">200 مليون دولار</div>
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 6px 12px; border-radius: 15px; cursor: pointer; font-size: 11px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 15px; margin-bottom: 15px; background: #fafafa;">
                            <h5 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">مشروع الإسكان الشعبي</h5>
                            <p style="margin: 0 0 10px 0; color: #666; font-size: 12px; line-height: 1.4;">بناء وحدات سكنية للطبقة المتوسطة في عدة محافظات</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 13px;">300 مليون دولار</div>
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 6px 12px; border-radius: 15px; cursor: pointer; font-size: 11px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 15px; margin-bottom: 15px; background: #fafafa;">
                            <h5 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">تطوير المنطقة الصناعية</h5>
                            <p style="margin: 0 0 10px 0; color: #666; font-size: 12px; line-height: 1.4;">تطوير وتحديث المنطقة الصناعية وإنشاء مصانع جديدة</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 13px;">150 مليون دولار</div>
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 6px 12px; border-radius: 15px; cursor: pointer; font-size: 11px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- منشورات البائعين (الجانب الأيمن) -->
            <div style="flex: 1; display: flex; flex-direction: column; align-items: flex-end;">
                <div style="margin-bottom: 22px;">
                    <h3 style="margin: 0; color: #333; font-size: 28px;">
                        <i class="fas fa-newspaper" style="color: #16CCC8; margin-left: 8px;"></i>
                        منشورات البائعين
                    </h3>
                </div>
                <div id="sellersPostsContainer" class="my-posts-container" style="font-size: 18px;">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                </div>
            </div>
        </div>
                    <div style="width: 15px; height: 45%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 90%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 70%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 85%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 95%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                </div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 10px; color: #666;">
                <span>السبت</span><span>الأحد</span><span>الاثنين</span><span>الثلاثاء</span><span>الأربعاء</span><span>الخميس</span><span>الجمعة</span>
            </div>
        </div>

        <!-- مؤشر الأداء الرئيسي -->
        <div style="
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 15px;
        ">
            <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">+12%</div>
            <div style="font-size: 14px; opacity: 0.9;">هذا الأسبوع</div>
            <div style="font-size: 12px; opacity: 0.8; margin-top: 3px;">📈 نمو مستمر</div>
        </div>

        <!-- الإحصائيات التفصيلية -->
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px;">
            <div style="
                background: #e8f5e8;
                border: 1px solid #c3e6c3;
                padding: 12px;
                border-radius: 10px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
            " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                <div style="color: #27ae60; font-size: 18px; margin-bottom: 4px;">
                    <i class="fas fa-home"></i>
                </div>
                <div style="color: #27ae60; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+8%</div>
                <div style="color: #666; font-size: 10px;">الشقق</div>
            </div>

            <div style="
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 12px;
                border-radius: 10px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
            " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                <div style="color: #e67e22; font-size: 18px; margin-bottom: 4px;">
                    <i class="fas fa-building"></i>
                </div>
                <div style="color: #e67e22; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+5%</div>
                <div style="color: #666; font-size: 10px;">التجاري</div>
            </div>
        </div>
    </div>

    <script>
        // تحميل قصص البائعين
        function loadSellersStories(serviceName) {
            const container = document.getElementById('sellersStoriesContainer');
            const stories = generateSellersStories(serviceName);

            container.innerHTML = stories.map(story => `
                <div style="
                    min-width: 75px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                    <div style="
                        width: 65px;
                        height: 65px;
                        border-radius: 50%;
                        background: linear-gradient(45deg, #16CCC8, #227FCC);
                        padding: 3px;
                        margin: 0 auto 8px;
                        box-shadow: 0 4px 15px rgba(22, 204, 200, 0.3);
                        transition: all 0.3s ease;
                    ">
                        <div style="
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            background: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            overflow: hidden;
                        ">
                            <img src="${story.avatar}" alt="${story.name}" style="
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                                border-radius: 50%;
                                transition: all 0.3s ease;
                            " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="
                                display: none;
                                width: 100%;
                                height: 100%;
                                align-items: center;
                                justify-content: center;
                                background: linear-gradient(45deg, #16CCC8, #227FCC);
                                color: white;
                                font-size: 20px;
                                border-radius: 50%;
                            ">
                                <i class="${story.icon}"></i>
                            </div>
                        </div>
                    </div>
                    <div style="
                        font-size: 11px;
                        color: #333;
                        font-weight: 600;
                        line-height: 1.2;
                        max-width: 75px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    ">
                        ${story.name.length > 8 ? story.name.substring(0, 8) + '...' : story.name}
                    </div>
                </div>
            `).join('');
        }

        // إنشاء قصص البائعين العقاريين فقط
        function generateSellersStories(serviceName) {
            const stories = [];

            // قصص العقارات فقط مع صور عقارية متنوعة (20 قصة)
            stories.push(
                { name: 'شركة العقارات الذهبية', avatar: '../get_me_app/صور عقارات/q1.jpg', icon: 'fas fa-home' },
                { name: 'مكتب الإسكان المتميز', avatar: '../get_me_app/صور عقارات/q2.jpg', icon: 'fas fa-building' },
                { name: 'عقارات بغداد الحديثة', avatar: '../get_me_app/صور عقارات/q3.jpg', icon: 'fas fa-city' },
                { name: 'عقارات الكرادة', avatar: '../get_me_app/صور عقارات/q4.jpg', icon: 'fas fa-home' },
                { name: 'مكتب الأراضي الاستثمارية', avatar: '../get_me_app/صور عقارات/q5.jpg', icon: 'fas fa-map-marked-alt' },
                { name: 'شركة البناء الحديث', avatar: '../get_me_app/صور عقارات/q6.jpg', icon: 'fas fa-hammer' },
                { name: 'عقارات الجادرية', avatar: '../get_me_app/صور عقارات/q7.jpg', icon: 'fas fa-building' },
                { name: 'مكتب الشقق الفاخرة', avatar: '../get_me_app/صور عقارات/q8.jpg', icon: 'fas fa-key' },
                { name: 'عقارات المنصور الراقية', avatar: '../get_me_app/صور عقارات/q9.jpg', icon: 'fas fa-crown' },
                { name: 'مكتب الفلل الفاخرة', avatar: '../get_me_app/صور عقارات/q10.jpg', icon: 'fas fa-chess-rook' },
                { name: 'شركة الاستثمار العقاري', avatar: '../get_me_app/صور عقارات/q11.jpg', icon: 'fas fa-chart-line' },
                { name: 'عقارات الزعفرانية', avatar: '../get_me_app/صور عقارات/q12.jpg', icon: 'fas fa-home' },
                { name: 'مكتب العقارات التجارية', avatar: '../get_me_app/صور عقارات/q13.jpg', icon: 'fas fa-store' },
                { name: 'شركة التطوير العقاري', avatar: '../get_me_app/صور عقارات/q14.jpg', icon: 'fas fa-tools' },
                { name: 'عقارات الكاظمية', avatar: '../get_me_app/صور عقارات/q15.jpg', icon: 'fas fa-mosque' },
                { name: 'مكتب الأراضي الزراعية', avatar: '../get_me_app/صور عقارات/q16.jpg', icon: 'fas fa-seedling' },
                { name: 'عقارات الرصافة', avatar: '../get_me_app/صور عقارات/q17.jpg', icon: 'fas fa-building' },
                { name: 'شركة الإسكان الشعبي', avatar: '../get_me_app/صور عقارات/q18.jpg', icon: 'fas fa-users' },
                { name: 'مكتب العقارات الدولية', avatar: '../get_me_app/صور عقارات/q19.jpg', icon: 'fas fa-globe' },
                { name: 'عقارات البصرة الحديثة', avatar: '../get_me_app/صور عقارات/q20.jpg', icon: 'fas fa-water' }
            );

            return stories;
        }

        // إنشاء البث المباشر العقاري
        function generateLiveStreams(serviceName) {
            const streams = [];

            // بث مباشر للعقارات (15 بث)
            streams.push(
                { name: 'عقارات الذهبية', avatar: '../get_me_app/صور عقارات/q1.jpg', icon: 'fas fa-home', isLive: true },
                { name: 'مكتب الإسكان', avatar: '../get_me_app/صور عقارات/q2.jpg', icon: 'fas fa-building', isLive: false },
                { name: 'بغداد الحديثة', avatar: '../get_me_app/صور عقارات/q3.jpg', icon: 'fas fa-city', isLive: true },
                { name: 'عقارات الكرادة', avatar: '../get_me_app/صور عقارات/q4.jpg', icon: 'fas fa-home', isLive: true },
                { name: 'الأراضي الاستثمارية', avatar: '../get_me_app/صور عقارات/q5.jpg', icon: 'fas fa-map-marked-alt', isLive: false },
                { name: 'البناء الحديث', avatar: '../get_me_app/صور عقارات/q6.jpg', icon: 'fas fa-hammer', isLive: true },
                { name: 'عقارات الجادرية', avatar: '../get_me_app/صور عقارات/q7.jpg', icon: 'fas fa-building', isLive: false },
                { name: 'الشقق الفاخرة', avatar: '../get_me_app/صور عقارات/q8.jpg', icon: 'fas fa-key', isLive: true },
                { name: 'المنصور الراقية', avatar: '../get_me_app/صور عقارات/q9.jpg', icon: 'fas fa-crown', isLive: false },
                { name: 'الفلل الفاخرة', avatar: '../get_me_app/صور عقارات/q10.jpg', icon: 'fas fa-chess-rook', isLive: true },
                { name: 'الاستثمار العقاري', avatar: '../get_me_app/صور عقارات/q11.jpg', icon: 'fas fa-chart-line', isLive: true },
                { name: 'عقارات الزعفرانية', avatar: '../get_me_app/صور عقارات/q12.jpg', icon: 'fas fa-home', isLive: false },
                { name: 'العقارات التجارية', avatar: '../get_me_app/صور عقارات/q13.jpg', icon: 'fas fa-store', isLive: true },
                { name: 'التطوير العقاري', avatar: '../get_me_app/صور عقارات/q14.jpg', icon: 'fas fa-tools', isLive: false },
                { name: 'عقارات الكاظمية', avatar: '../get_me_app/صور عقارات/q15.jpg', icon: 'fas fa-mosque', isLive: true }
            );

            return streams;
        }

        // إنشاء العروض المميزة العقارية
        function generatePremiumOffers(serviceName) {
            const offers = [];

            // عروض عقارية مميزة (12 عرض)
            offers.push(
                {
                    title: 'شقة 3 غرف للبيع - الكرادة',
                    description: 'شقة فاخرة في منطقة راقية مع جميع الخدمات، مطبخ مجهز، موقف سيارة',
                    price: '150,000$',
                    seller: 'شركة العقارات الذهبية',
                    icon: 'fas fa-home',
                    image: 'q1.jpg'
                },
                {
                    title: 'أرض استثمارية - بغداد الجديدة',
                    description: 'أرض في موقع استراتيجي مميز للاستثمار، 500 متر مربع',
                    price: '200,000$',
                    seller: 'مكتب الإسكان المتميز',
                    icon: 'fas fa-map-marked-alt',
                    image: 'q2.jpg'
                },
                {
                    title: 'فيلا فاخرة - الجادرية',
                    description: 'فيلا مع حديقة وحمام سباحة، تصميم عصري، 4 غرف نوم',
                    price: '450,000$',
                    seller: 'عقارات الجادرية الراقية',
                    icon: 'fas fa-home',
                    image: 'q3.jpg'
                },
                {
                    title: 'شقة للإيجار - المنصور',
                    description: 'شقة مفروشة بالكامل، 2 غرف نوم، قريبة من الخدمات',
                    price: '800$ شهرياً',
                    seller: 'مكتب الإيجارات المتميز',
                    icon: 'fas fa-key',
                    image: 'q4.jpg'
                },
                {
                    title: 'مجمع تجاري للبيع',
                    description: 'مجمع تجاري في منطقة حيوية، عائد استثماري ممتاز',
                    price: '800,000$',
                    seller: 'شركة الاستثمار العقاري',
                    icon: 'fas fa-building',
                    image: 'q5.jpg'
                },
                {
                    title: 'بيت شعبي للبيع - الكاظمية',
                    description: 'بيت تراثي أصيل، 4 غرف، حوش واسع، قريب من المرقد الشريف',
                    price: '120,000$',
                    seller: 'عقارات الكاظمية',
                    icon: 'fas fa-mosque',
                    image: 'q6.jpg'
                },
                {
                    title: 'شقة دوبلكس - الزعفرانية',
                    description: 'شقة دوبلكس حديثة، 5 غرف، تراس كبير، إطلالة رائعة',
                    price: '280,000$',
                    seller: 'عقارات الزعفرانية',
                    icon: 'fas fa-building',
                    image: 'q7.jpg'
                },
                {
                    title: 'محل تجاري للإيجار - الكرادة',
                    description: 'محل في شارع تجاري مزدحم، مساحة 80 متر، واجهة زجاجية',
                    price: '1,200$ شهرياً',
                    seller: 'مكتب العقارات التجارية',
                    icon: 'fas fa-store',
                    image: 'q8.jpg'
                },
                {
                    title: 'قطعة أرض زراعية - أبو غريب',
                    description: 'أرض زراعية خصبة، 2000 متر، مع بئر ماء، صالحة للزراعة',
                    price: '80,000$',
                    seller: 'مكتب الأراضي الزراعية',
                    icon: 'fas fa-seedling',
                    image: 'q9.jpg'
                },
                {
                    title: 'شقة استوديو - الجامعة',
                    description: 'شقة صغيرة مناسبة للطلاب، مفروشة، قريبة من الجامعة',
                    price: '400$ شهرياً',
                    seller: 'عقارات الطلاب',
                    icon: 'fas fa-graduation-cap',
                    image: 'q10.jpg'
                },
                {
                    title: 'مكتب للإيجار - المنطقة الخضراء',
                    description: 'مكتب في برج حديث، 3 غرف، مكيف، أمان 24 ساعة',
                    price: '1,500$ شهرياً',
                    seller: 'مكتب العقارات الدولية',
                    icon: 'fas fa-briefcase',
                    image: 'q11.jpg'
                },
                {
                    title: 'قصر فاخر للبيع - الجادرية',
                    description: 'قصر على نهر دجلة، حديقة واسعة، مسبح، تصميم كلاسيكي فاخر',
                    price: '1,200,000$',
                    seller: 'عقارات القصور الفاخرة',
                    icon: 'fas fa-crown',
                    image: 'q12.jpg'
                }
            );

            return offers;
        }

        // إنشاء منشورات البائعين من دول عربية مختلفة
        function generateSellersPosts(serviceName) {
            const posts = [];

            // منشورات عقارية من دول عربية مختلفة (20 منشور)
            posts.push(
                // العراق
                {
                    id: 1,
                    author: 'شركة العقارات الذهبية - بغداد',
                    time: 'منذ ساعة',
                    country: 'العراق',
                    countryFlag: '🇮🇶',
                    countryName: 'العراق',
                    countryColor: '#007A3D',
                    content: '🏠 شقة للبيع في منطقة الكرادة - 3 غرف نوم، 2 حمام، مطبخ مجهز، موقف سيارة. المساحة 120 متر مربع. السعر: 150,000$ قابل للتفاوض.',
                    images: ['../get_me_app/صور عقارات/q1.jpg', '../get_me_app/صور عقارات/q2.jpg'],
                    avatar: '../get_me_app/صور عقارات/q1.jpg',
                    likes: 189,
                    comments: 67,
                    shares: 23
                },
                // مصر
                {
                    id: 2,
                    author: 'عقارات القاهرة الحديثة',
                    time: 'منذ 2 ساعة',
                    country: 'مصر',
                    countryFlag: '🇪🇬',
                    countryName: 'مصر',
                    countryColor: '#CE1126',
                    content: '🏢 شقة فاخرة في التجمع الخامس - 4 غرف، 3 حمامات، تشطيب سوبر لوكس، إطلالة على النيل. المساحة 180 متر. السعر: 2,500,000 جنيه مصري.',
                    images: ['../get_me_app/صور عقارات/q3.jpg'],
                    avatar: '../get_me_app/صور عقارات/q3.jpg',
                    likes: 234,
                    comments: 89,
                    shares: 45
                },
                // الإمارات
                {
                    id: 3,
                    author: 'عقارات دبي الفاخرة',
                    time: 'منذ 3 ساعات',
                    country: 'الإمارات',
                    countryFlag: '🇦🇪',
                    countryName: 'الإمارات',
                    countryColor: '#00732F',
                    content: '🏗️ برج سكني جديد في دبي مارينا! شقق بإطلالة بحرية، مرافق 5 نجوم، جيم، مسبح، أمن 24/7. أسعار تبدأ من 1,200,000 درهم. خطط دفع مرنة.',
                    images: ['../get_me_app/صور عقارات/q4.jpg', '../get_me_app/صور عقارات/q5.jpg'],
                    avatar: '../get_me_app/صور عقارات/q4.jpg',
                    likes: 456,
                    comments: 123,
                    shares: 78
                },
                // السعودية
                {
                    id: 4,
                    author: 'مجموعة الرياض العقارية',
                    time: 'منذ 4 ساعات',
                    country: 'السعودية',
                    countryFlag: '🇸🇦',
                    countryName: 'السعودية',
                    countryColor: '#006C35',
                    content: '🕌 فيلا في حي الملقا - الرياض! 5 غرف نوم، مجلس رجال، مجلس نساء، حديقة واسعة، مسبح. المساحة 400 متر. السعر: 1,800,000 ريال سعودي.',
                    images: ['../get_me_app/صور عقارات/q6.jpg'],
                    avatar: '../get_me_app/صور عقارات/q6.jpg',
                    likes: 378,
                    comments: 156,
                    shares: 67
                },
                // الكويت
                {
                    id: 5,
                    author: 'شركة الكويت العقارية',
                    time: 'منذ 5 ساعات',
                    country: 'الكويت',
                    countryFlag: '🇰🇼',
                    countryName: 'الكويت',
                    countryColor: '#007A3D',
                    content: '🏠 شقة للإيجار في السالمية! 3 غرف، مفروشة بالكامل، إطلالة بحرية، قريبة من المراكز التجارية. الإيجار: 600 دينار كويتي شهرياً.',
                    images: ['../get_me_app/صور عقارات/q7.jpg', '../get_me_app/صور عقارات/q8.jpg'],
                    avatar: '../get_me_app/صور عقارات/q7.jpg',
                    likes: 267,
                    comments: 89,
                    shares: 34
                },
                // الأردن
                {
                    id: 6,
                    author: 'عقارات عمان الحديثة',
                    time: 'منذ 6 ساعات',
                    country: 'الأردن',
                    countryFlag: '🇯🇴',
                    countryName: 'الأردن',
                    countryColor: '#CE1126',
                    content: '🏘️ شقة في عبدون - عمان! 2 غرف نوم، صالة واسعة، مطبخ حديث، شرفة. منطقة راقية وهادئة. السعر: 85,000 دينار أردني.',
                    images: ['../get_me_app/صور عقارات/q9.jpg'],
                    avatar: '../get_me_app/صور عقارات/q9.jpg',
                    likes: 198,
                    comments: 76,
                    shares: 29
                },
                // سوريا
                {
                    id: 7,
                    author: 'مكتب دمشق للعقارات',
                    time: 'منذ 7 ساعات',
                    country: 'سوريا',
                    countryFlag: '🇸🇾',
                    countryName: 'سوريا',
                    countryColor: '#CE1126',
                    content: '🏛️ بيت دمشقي أثري في البلدة القديمة! معمار تراثي أصيل، فناء داخلي، 6 غرف، مرمم بالكامل. قطعة تاريخية نادرة. السعر: 300,000$',
                    images: ['../get_me_app/صور عقارات/q10.jpg', '../get_me_app/صور عقارات/q11.jpg'],
                    avatar: '../get_me_app/صور عقارات/q10.jpg',
                    likes: 345,
                    comments: 134,
                    shares: 89
                },
                // لبنان
                {
                    id: 8,
                    author: 'عقارات بيروت الراقية',
                    time: 'منذ 8 ساعات',
                    country: 'لبنان',
                    countryFlag: '🇱🇧',
                    countryName: 'لبنان',
                    countryColor: '#ED1C24',
                    content: '🌊 شقة بإطلالة بحرية في الروشة! 3 غرف، تراس كبير، تشطيب فاخر، قريبة من الكورنيش. السعر: 250,000$ نقداً فقط.',
                    images: ['../get_me_app/صور عقارات/q12.jpg'],
                    avatar: '../get_me_app/صور عقارات/q12.jpg',
                    likes: 289,
                    comments: 98,
                    shares: 56
                },
                // قطر
                {
                    id: 9,
                    author: 'مجموعة الدوحة العقارية',
                    time: 'منذ 9 ساعات',
                    country: 'قطر',
                    countryFlag: '🇶🇦',
                    countryName: 'قطر',
                    countryColor: '#8D1B3D',
                    content: '🏗️ برج سكني جديد في الخليج الغربي! شقق فاخرة مع خدمات فندقية، مسبح، جيم، سبا. أسعار تبدأ من 1,500,000 ريال قطري.',
                    images: ['../get_me_app/صور عقارات/q15.jpg', '../get_me_app/صور عقارات/q16.jpg'],
                    avatar: '../get_me_app/صور عقارات/q15.jpg',
                    likes: 567,
                    comments: 234,
                    shares: 123
                },
                // البحرين
                {
                    id: 10,
                    author: 'عقارات المنامة المتميزة',
                    time: 'منذ 10 ساعات',
                    country: 'البحرين',
                    countryFlag: '🇧🇭',
                    countryName: 'البحرين',
                    countryColor: '#CE1126',
                    content: '🏖️ فيلا على البحر في عمواج! 4 غرف، حديقة خاصة، شاطئ خاص، مرسى لليخت. تصميم معماري فريد. السعر: 800,000 دينار بحريني.',
                    images: ['../get_me_app/صور عقارات/q17.jpg'],
                    avatar: '../get_me_app/صور عقارات/q17.jpg',
                    likes: 423,
                    comments: 167,
                    shares: 89
                },
                // عمان
                {
                    id: 11,
                    author: 'شركة مسقط للاستثمار العقاري',
                    time: 'منذ 11 ساعة',
                    country: 'عمان',
                    countryFlag: '🇴🇲',
                    countryName: 'عمان',
                    countryColor: '#ED1C24',
                    content: '🏔️ فيلا في مسقط هيلز! إطلالة جبلية رائعة، 5 غرف، حديقة استوائية، مسبح لا متناهي. تصميم عماني أصيل. السعر: 300,000 ريال عماني.',
                    images: ['../get_me_app/صور عقارات/q13.jpg', '../get_me_app/صور عقارات/q14.jpg'],
                    avatar: '../get_me_app/صور عقارات/q13.jpg',
                    likes: 312,
                    comments: 145,
                    shares: 67
                },
                // المغرب
                {
                    id: 12,
                    author: 'عقارات الدار البيضاء الفاخرة',
                    time: 'منذ 12 ساعة',
                    country: 'المغرب',
                    countryFlag: '🇲🇦',
                    countryName: 'المغرب',
                    countryColor: '#C1272D',
                    content: '🕌 رياض تقليدي في المدينة القديمة! معمار مغربي أصيل، فناء بنافورة، 8 غرف، حمام تقليدي. قطعة تراثية نادرة. السعر: 2,800,000 درهم مغربي.',
                    image: '../get_me_app/صور عقارات/q12.jpg',
                    avatar: '../get_me_app/صور عقارات/q12.jpg',
                    likes: 456,
                    comments: 189,
                    shares: 123
                },
                // تونس
                {
                    id: 13,
                    author: 'مكتب تونس العاصمة العقاري',
                    time: 'منذ 13 ساعة',
                    country: 'تونس',
                    countryFlag: '🇹🇳',
                    countryName: 'تونس',
                    countryColor: '#E70013',
                    content: '🏖️ فيلا في سيدي بوسعيد! إطلالة على البحر المتوسط، تصميم أندلسي، 4 غرف، تراس واسع. منطقة سياحية مميزة. السعر: 450,000 دينار تونسي.',
                    avatar: '../get_me_app/صور عقارات/q13.jpg',
                    likes: 234,
                    comments: 98,
                    shares: 45
                },
                // الجزائر
                {
                    id: 14,
                    author: 'شركة الجزائر العقارية',
                    time: 'منذ 14 ساعة',
                    country: 'الجزائر',
                    countryFlag: '🇩🇿',
                    countryName: 'الجزائر',
                    countryColor: '#006233',
                    content: '🏠 شقة في حي الأبيار - الجزائر العاصمة! 3 غرف، مطبخ مجهز، شرفة، موقف سيارة. قريبة من المترو والخدمات. السعر: 8,500,000 دينار جزائري.',
                    image: '../get_me_app/صور عقارات/q14.jpg',
                    avatar: '../get_me_app/صور عقارات/q14.jpg',
                    likes: 178,
                    comments: 67,
                    shares: 34
                },
                // ليبيا
                {
                    id: 15,
                    author: 'مكتب طرابلس للعقارات',
                    time: 'منذ 15 ساعة',
                    country: 'ليبيا',
                    countryFlag: '🇱🇾',
                    countryName: 'ليبيا',
                    countryColor: '#E70013',
                    content: '🏛️ بيت تراثي في المدينة القديمة - طرابلس! معمار عثماني أصيل، فناء داخلي، 6 غرف. موقع تاريخي مميز. السعر: 180,000 دينار ليبي.',
                    avatar: '../get_me_app/صور عقارات/q15.jpg',
                    likes: 156,
                    comments: 78,
                    shares: 23
                },
                // السودان
                {
                    id: 16,
                    author: 'عقارات الخرطوم الحديثة',
                    time: 'منذ 16 ساعة',
                    country: 'السودان',
                    countryFlag: '🇸🇩',
                    countryName: 'السودان',
                    countryColor: '#D21034',
                    content: '🏘️ مجمع سكني في الخرطوم 2! شقق حديثة، مرافق متكاملة، حديقة، ملعب أطفال، أمن. أسعار تبدأ من 45,000,000 جنيه سوداني.',
                    image: '../get_me_app/صور عقارات/q16.jpg',
                    avatar: '../get_me_app/صور عقارات/q16.jpg',
                    likes: 123,
                    comments: 56,
                    shares: 19
                },
                // فلسطين
                {
                    id: 17,
                    author: 'مكتب رام الله العقاري',
                    time: 'منذ 17 ساعة',
                    country: 'فلسطين',
                    countryFlag: '🇵🇸',
                    countryName: 'فلسطين',
                    countryColor: '#007A3D',
                    content: '🕊️ شقة في رام الله! 3 غرف، إطلالة على الجبال، تشطيب حديث، قريبة من الجامعات. منطقة هادئة ومميزة. السعر: 65,000 دولار أمريكي.',
                    avatar: '../get_me_app/صور عقارات/q17.jpg',
                    likes: 289,
                    comments: 123,
                    shares: 78
                },
                // اليمن
                {
                    id: 18,
                    author: 'شركة صنعاء العقارية',
                    time: 'منذ 18 ساعة',
                    country: 'اليمن',
                    countryFlag: '🇾🇪',
                    countryName: 'اليمن',
                    countryColor: '#CE1126',
                    content: '🏠 بيت يمني تقليدي في صنعاء القديمة! معمار يمني أصيل، نوافذ قمرية، فناء داخلي، 5 غرف. موقع تراثي مميز. السعر: 25,000,000 ريال يمني.',
                    image: '../get_me_app/صور عقارات/q18.jpg',
                    avatar: '../get_me_app/صور عقارات/q18.jpg',
                    likes: 167,
                    comments: 89,
                    shares: 45
                },
                // العراق (إضافي)
                {
                    id: 19,
                    author: 'عقارات البصرة الحديثة',
                    time: 'منذ 19 ساعة',
                    country: 'العراق',
                    countryFlag: '🇮🇶',
                    countryName: 'العراق',
                    countryColor: '#007A3D',
                    content: '🛥️ فيلا على شط العرب - البصرة! إطلالة نهرية مباشرة، 4 غرف، حديقة، مرسى خاص. موقع استثنائي هادئ. السعر: 320,000$ قابل للتفاوض.',
                    image: '../get_me_app/صور عقارات/q19.jpg',
                    avatar: '../get_me_app/صور عقارات/q19.jpg',
                    likes: 234,
                    comments: 112,
                    shares: 67
                },
                // مصر (إضافي)
                {
                    id: 20,
                    author: 'عقارات الإسكندرية البحرية',
                    time: 'منذ 20 ساعة',
                    country: 'مصر',
                    countryFlag: '🇪🇬',
                    countryName: 'مصر',
                    countryColor: '#CE1126',
                    content: '🏖️ شاليه في مارينا الساحل الشمالي! إطلالة بحرية مباشرة، 2 غرف، تراس، مسبح مشترك، شاطئ خاص. السعر: 1,800,000 جنيه مصري.',
                    images: ['../get_me_app/صور عقارات/q19.jpg', '../get_me_app/صور عقارات/q20.jpg'],
                    avatar: '../get_me_app/صور عقارات/q19.jpg',
                    likes: 389,
                    comments: 156,
                    shares: 89
                },
                // العراق (إضافي 2)
                {
                    id: 21,
                    author: 'عقارات أربيل الحديثة',
                    time: 'منذ 21 ساعة',
                    country: 'العراق',
                    countryFlag: '🇮🇶',
                    countryName: 'العراق',
                    countryColor: '#007A3D',
                    content: '🏔️ فيلا في أربيل! منطقة هادئة وآمنة، 4 غرف، حديقة، مرآب، تصميم كردي حديث. قريبة من المراكز التجارية. السعر: 180,000$',
                    images: ['../get_me_app/صور عقارات/q1.jpg'],
                    avatar: '../get_me_app/صور عقارات/q1.jpg',
                    likes: 156,
                    comments: 78,
                    shares: 34
                },
                // الإمارات (إضافي)
                {
                    id: 22,
                    author: 'عقارات أبوظبي الراقية',
                    time: 'منذ 22 ساعة',
                    country: 'الإمارات',
                    countryFlag: '🇦🇪',
                    countryName: 'الإمارات',
                    countryColor: '#00732F',
                    content: '🏝️ شقة في جزيرة ياس! إطلالة على حلبة الفورمولا 1، 3 غرف، مرافق ترفيهية، قريبة من فيراري وورلد. السعر: 2,200,000 درهم.',
                    images: ['../get_me_app/صور عقارات/q2.jpg', '../get_me_app/صور عقارات/q3.jpg'],
                    avatar: '../get_me_app/صور عقارات/q2.jpg',
                    likes: 567,
                    comments: 234,
                    shares: 123
                },
                // السعودية (إضافي)
                {
                    id: 23,
                    author: 'عقارات جدة البحرية',
                    time: 'منذ 23 ساعة',
                    country: 'السعودية',
                    countryFlag: '🇸🇦',
                    countryName: 'السعودية',
                    countryColor: '#006C35',
                    content: '🌊 شقة بإطلالة بحرية في جدة! كورنيش جدة، 3 غرف، صالة واسعة، مطبخ مفتوح، شرفة بحرية. السعر: 850,000 ريال سعودي.',
                    images: ['../get_me_app/صور عقارات/q4.jpg'],
                    avatar: '../get_me_app/صور عقارات/q4.jpg',
                    likes: 298,
                    comments: 134,
                    shares: 67
                },
                // الكويت (إضافي)
                {
                    id: 24,
                    author: 'مكتب الكويت للعقارات الفاخرة',
                    time: 'منذ 24 ساعة',
                    country: 'الكويت',
                    countryFlag: '🇰🇼',
                    countryName: 'الكويت',
                    countryColor: '#007A3D',
                    content: '🏖️ فيلا في المهبولة! إطلالة بحرية، 5 غرف، حديقة، مسبح، مرسى خاص. منطقة راقية وهادئة. السعر: 450,000 دينار كويتي.',
                    images: ['../get_me_app/صور عقارات/q5.jpg', '../get_me_app/صور عقارات/q6.jpg'],
                    avatar: '../get_me_app/صور عقارات/q5.jpg',
                    likes: 423,
                    comments: 189,
                    shares: 89
                },
                // الأردن (إضافي)
                {
                    id: 25,
                    author: 'شركة العقبة السياحية',
                    time: 'منذ 25 ساعة',
                    country: 'الأردن',
                    countryFlag: '🇯🇴',
                    countryName: 'الأردن',
                    countryColor: '#CE1126',
                    content: '🏖️ شاليه في العقبة! إطلالة على البحر الأحمر، 2 غرف، تراس، قريب من المرافق السياحية. مثالي للاستثمار السياحي. السعر: 65,000 دينار أردني.',
                    images: ['../get_me_app/صور عقارات/q7.jpg'],
                    avatar: '../get_me_app/صور عقارات/q7.jpg',
                    likes: 234,
                    comments: 98,
                    shares: 45
                }
            );

            return posts;
        }

        // دوال التمرير للقصص
        function scrollStories(direction) {
            const container = document.getElementById('sellersStoriesContainer');
            const scrollAmount = 400; // زيادة مسافة التمرير لتناسب العدد الكبير

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updateArrowsState();
            }, 400);
        }

        // تحديث حالة الأسهم
        function updateArrowsState() {
            const container = document.getElementById('sellersStoriesContainer');
            const leftArrow = document.getElementById('storiesLeftArrow');
            const rightArrow = document.getElementById('storiesRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دوال التمرير للبث المباشر
        function scrollLiveStreams(direction) {
            const container = document.getElementById('liveStreamsContainer');
            const scrollAmount = 400;

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updateLiveArrowsState();
            }, 400);
        }

        // تحديث حالة أسهم البث المباشر
        function updateLiveArrowsState() {
            const container = document.getElementById('liveStreamsContainer');
            const leftArrow = document.getElementById('liveLeftArrow');
            const rightArrow = document.getElementById('liveRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دالة بدء بث مباشر
        function startLiveStream() {
            alert('🔴 بدء بث مباشر!\n\nسيتم فتح نافذة البث المباشر...\n\n📹 يمكنك بث فيديو مباشر\n💬 التفاعل مع المشاهدين\n📊 مراقبة الإحصائيات');
        }

        // دوال التمرير للعروض المميزة
        function scrollPremiumOffers(direction) {
            const container = document.getElementById('premiumOffersContainer');
            const scrollAmount = 400;

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updatePremiumArrowsState();
            }, 400);
        }

        // تحديث حالة أسهم العروض المميزة
        function updatePremiumArrowsState() {
            const container = document.getElementById('premiumOffersContainer');
            const leftArrow = document.getElementById('premiumLeftArrow');
            const rightArrow = document.getElementById('premiumRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دالة فتح العرض المميز
        function openServicePremiumOffer(title) {
            alert('⭐ عرض مميز!\n\n' + title + '\n\nسيتم فتح تفاصيل العرض...\n\n💰 معلومات السعر\n📞 تفاصيل التواصل\n📍 الموقع على الخريطة');
        }

        // تحميل منشورات البائعين (تصميم Profile.html)
        function loadSellersPosts(serviceName) {
            const container = document.getElementById('sellersPostsContainer');
            const posts = generateSellersPosts(serviceName);

            container.innerHTML = posts.map(post => `
                <div class="post-card">
                    <div class="post-header">
                        <div class="post-avatar">
                            <img src="${post.avatar}" alt="${post.author}">
                        </div>
                        <div class="post-info">
                            <h4>${post.author}</h4>
                            <div class="post-time">${post.time} • ${post.country}</div>
                        </div>
                        <div style="
                            background: ${post.countryColor};
                            color: white;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: bold;
                        ">
                            ${post.countryFlag} ${post.countryName}
                        </div>
                    </div>
                    <div class="post-content">${post.content}</div>
                    ${post.images && post.images.length > 0 ? `
                        <div class="post-images" style="margin: 15px 0; display: ${post.images.length > 1 ? 'grid' : 'block'}; ${post.images.length > 1 ? 'grid-template-columns: 1fr 1fr; gap: 10px;' : ''}">
                            ${post.images.map(img => `
                                <img src="${img}" alt="صورة المنشور" style="
                                    width: 100%;
                                    height: ${post.images.length > 1 ? '200px' : '250px'};
                                    object-fit: cover;
                                    border-radius: 10px;
                                    cursor: pointer;
                                " onclick="openImageModal('${img}')">
                            `).join('')}
                        </div>
                    ` : ''}
                    <div class="post-stats" style="display: flex; justify-content: space-around; padding: 10px 0; border-top: 1px solid #f0f0f0; border-bottom: 1px solid #f0f0f0; margin: 15px 0; font-size: 14px; color: #666;">
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-heart" style="color: #16CCC8;"></i>
                            <span>${post.likes}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-comment" style="color: #16CCC8;"></i>
                            <span>${post.comments}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-share" style="color: #16CCC8;"></i>
                            <span>${post.shares}</span>
                        </div>
                    </div>
                    <div class="post-actions">
                        <div class="post-action" onclick="likePost(${post.id})">
                            <i class="fas fa-heart"></i>
                            <span>إعجاب</span>
                        </div>
                        <div class="post-action" onclick="commentPost(${post.id})">
                            <i class="fas fa-comment"></i>
                            <span>تعليق</span>
                        </div>
                        <div class="post-action" onclick="sharePost(${post.id})">
                            <i class="fas fa-share"></i>
                            <span>مشاركة</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // تحميل العروض المميزة
        function loadPremiumOffers(serviceName) {
            const container = document.getElementById('premiumOffersContainer');
            const offers = generatePremiumOffers(serviceName);

            container.innerHTML = offers.map(offer => `
                <div class="premium-offer-card" onclick="openServicePremiumOffer('${offer.title}')" style="
                    width: 380px;
                    height: 200px;
                    border-radius: 15px;
                    border: 3px solid #FFD700;
                    background: linear-gradient(135deg, #FFF8DC, #FFFACD);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    cursor: pointer;
                    transition: all 0.3s;
                    position: relative;
                    flex-shrink: 0;
                    overflow: hidden;
                ">
                    <div style="
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
                        animation: shimmer 3s infinite;
                    "></div>

                    <div style="
                        width: 100%;
                        height: 100%;
                        position: relative;
                        z-index: 2;
                        overflow: hidden;
                        border-radius: 12px;
                    ">
                        <img src="../get_me_app/صور عقارات/${offer.image}" alt="${offer.title}" style="
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            border-radius: 12px;
                        " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="
                            display: none;
                            width: 100%;
                            height: 100%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 48px;
                            border-radius: 12px;
                        ">
                            <i class="${offer.icon}"></i>
                        </div>

                        <div style="
                            position: absolute;
                            top: 0;
                            right: 0;
                            background: linear-gradient(135deg, rgba(0,0,0,0.5), rgba(0,0,0,0.3));
                            color: white;
                            padding: 15px;
                            z-index: 3;
                            border-radius: 0 12px 0 15px;
                            max-width: 70%;
                        ">
                            <div style="
                                font-size: 14px;
                                font-weight: bold;
                                margin-bottom: 3px;
                                text-shadow: 2px 2px 4px rgba(0,0,0,0.9);
                                line-height: 1.2;
                            ">${offer.title}</div>
                            <div style="
                                font-size: 11px;
                                opacity: 0.95;
                                text-shadow: 1px 1px 3px rgba(0,0,0,0.9);
                                line-height: 1.3;
                            ">${offer.description}</div>
                        </div>
                    </div>

                    <div style="
                        position: absolute;
                        bottom: 5px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 200px;
                        height: 35px;
                        z-index: 10;
                    ">
                        <div style="
                            background: linear-gradient(135deg, #FFD700, #FFA500);
                            color: #8B4513;
                            padding: 8px 16px;
                            border-radius: 20px;
                            font-weight: bold;
                            font-size: 12px;
                            text-align: center;
                            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
                            border: 2px solid #FFD700;
                        ">عرض مميز - ${offer.price}</div>
                    </div>
                </div>
            `).join('');
        }

        // دالة إنشاء قصة جديدة
        function createNewStory() {
            alert('🎬 إنشاء قصة جديدة!\n\nسيتم فتح نافذة إنشاء القصة...\n\n📸 يمكنك إضافة صور وفيديوهات\n📝 كتابة وصف للقصة\n🏷️ إضافة علامات تجارية');
        }

        // دوال التفاعل مع المنشورات
        function likePost(postId) {
            alert('❤️ تم الإعجاب بالمنشور رقم ' + postId);
        }

        function commentPost(postId) {
            alert('💬 إضافة تعليق على المنشور رقم ' + postId);
        }

        function sharePost(postId) {
            alert('📤 مشاركة المنشور رقم ' + postId);
        }

        function openImageModal(imageSrc) {
            alert('🖼️ عرض الصورة بحجم كامل:\n' + imageSrc);
        }

        // دالة تغيير نوع الرسم البياني
        function changeChartType(type) {
            // إزالة التحديد من جميع الأزرار
            document.getElementById('weeklyBtn').style.background = 'transparent';
            document.getElementById('weeklyBtn').style.color = '#666';
            document.getElementById('monthlyBtn').style.background = 'transparent';
            document.getElementById('monthlyBtn').style.color = '#666';
            document.getElementById('yearlyBtn').style.background = 'transparent';
            document.getElementById('yearlyBtn').style.color = '#666';

            // تحديد الزر المختار
            const selectedBtn = document.getElementById(type + 'Btn');
            selectedBtn.style.background = 'linear-gradient(135deg, #16CCC8, #227FCC)';
            selectedBtn.style.color = 'white';

            // تحديث العنوان
            const chartTitle = document.getElementById('chartTitle');
            if (type === 'weekly') {
                chartTitle.textContent = '📊 الأداء الأسبوعي';
            } else if (type === 'monthly') {
                chartTitle.textContent = '📊 الأداء الشهري';
            } else {
                chartTitle.textContent = '📊 الأداء السنوي';
            }

            console.log('تم تغيير نوع الرسم البياني إلى:', type);
        }

        // تحميل البث المباشر
        function loadLiveStreams(serviceName) {
            const container = document.getElementById('liveStreamsContainer');
            const streams = generateLiveStreams(serviceName);

            container.innerHTML = streams.map(stream => `
                <div style="
                    min-width: 75px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    position: relative;
                " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                    <div style="
                        width: 65px;
                        height: 65px;
                        border-radius: 50%;
                        background: linear-gradient(45deg, #8e44ad, #e74c3c, #f39c12, #f1c40f);
                        padding: 3px;
                        margin: 0 auto 8px;
                        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
                        transition: all 0.3s ease;
                        position: relative;
                    ">
                        <div style="
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            background: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            overflow: hidden;
                        ">
                            <img src="${stream.avatar}" alt="${stream.name}" style="
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                                border-radius: 50%;
                            " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="
                                display: none;
                                width: 100%;
                                height: 100%;
                                align-items: center;
                                justify-content: center;
                                background: linear-gradient(45deg, #8e44ad, #e74c3c);
                                color: white;
                                font-size: 18px;
                                border-radius: 50%;
                            ">
                                <i class="${stream.icon}"></i>
                            </div>
                        </div>
                        ${stream.isLive ? `
                            <div style="
                                position: absolute;
                                bottom: 2px;
                                right: 2px;
                                background: #e74c3c;
                                color: white;
                                border-radius: 50%;
                                width: 18px;
                                height: 18px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 8px;
                                font-weight: bold;
                                border: 2px solid white;
                                animation: pulse 1.5s infinite;
                            ">
                                LIVE
                            </div>
                        ` : ''}
                    </div>
                    <div style="
                        font-size: 11px;
                        color: #333;
                        font-weight: 600;
                        line-height: 1.2;
                        max-width: 75px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    ">
                        ${stream.name.length > 8 ? stream.name.substring(0, 8) + '...' : stream.name}
                    </div>
                </div>
            `).join('');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل قصص البائعين للعقارات
            loadSellersStories('عقارات');

            // تحميل البث المباشر للعقارات
            loadLiveStreams('عقارات');

            // تحميل العروض المميزة للعقارات
            loadPremiumOffers('عقارات');

            // تحميل منشورات البائعين للعقارات
            loadSellersPosts('عقارات');

            // تحديث حالة الأسهم عند التحميل
            setTimeout(() => {
                updateArrowsState();
                updateLiveArrowsState();
                updatePremiumArrowsState();
            }, 100);

            // تحديث الأسهم عند التمرير اليدوي
            const storiesContainer = document.getElementById('sellersStoriesContainer');
            const liveContainer = document.getElementById('liveStreamsContainer');
            const premiumContainer = document.getElementById('premiumOffersContainer');

            storiesContainer.addEventListener('scroll', updateArrowsState);
            liveContainer.addEventListener('scroll', updateLiveArrowsState);
            premiumContainer.addEventListener('scroll', updatePremiumArrowsState);

            console.log('تم تحميل شريط القصص بنجاح! 🎉');
            console.log('تم تحميل شريط البث المباشر بنجاح! 🔴');
            console.log('تم تحميل العروض المميزة بنجاح! ⭐');
            console.log('تم تحميل منشورات البائعين بنجاح! 📰');
            console.log('عدد القصص المحملة:', generateSellersStories('عقارات').length);
            console.log('عدد البث المباشر:', generateLiveStreams('عقارات').length);
            console.log('عدد العروض المميزة:', generatePremiumOffers('عقارات').length);
            console.log('عدد المنشورات:', generateSellersPosts('عقارات').length);
        });
    </script>
</body>
</html>
