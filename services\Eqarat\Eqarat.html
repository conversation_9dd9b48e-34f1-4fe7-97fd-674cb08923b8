<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الخدمات - Get Me</title>
            <style>
                #selectedCountryName span, #pricesCountrySelectBox span {
                    font-family: 'Segoe UI Emoji', 'Noto Color Emoji', Arial, sans-serif !important;
                }
            </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* منع التمرير الأفقي غير المرغوب فيه */
        html, body {
            overflow-x: hidden;
            max-width: 100%;
        }

        /* منع التمرير الأفقي للحاويات الرئيسية */
        .main-content {
        .seller-status-label {
            position: absolute;
            top: 38px;
            left: 12px;
            font-size: 16px;
            font-weight: 500;
            color: #227FCC;
            z-index: 2;
            letter-spacing: 0.2px;
            background: none;
            border-radius: 0;
            padding: 0;
        }
        .seller-status-sold {
            color: #e74c3c;
        }
        .seller-status-available {
            color: #227FCC;
        }
        /* إعادة تنسيق كارت منشور البائع */
        .seller-post-card {
            border: 1.2px solid #e4e6ea;
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            transition: border-color 0.2s, box-shadow 0.2s;
            position: relative;
            padding: 18px 16px 18px 16px;
        }
        .seller-post-card:hover {
            border-color: #16CCC8;
            box-shadow: 0 4px 16px rgba(34,127,204,0.10);
        }
        .seller-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            margin-left: 10px;
        }
        .seller-header-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        .seller-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .seller-name {
            font-size: 15px;
            font-weight: bold;
            color: #227FCC;
        }
        .seller-time {
            font-size: 12px;
            color: #888;
            margin-top: 2px;
        }
        .seller-flag-img {
            position: absolute;
            top: 12px;
            left: 12px;
            width: 26px;
            height: 18px;
            border-radius: 4px;
            object-fit: cover;
            box-shadow: 0 1px 4px rgba(0,0,0,0.10);
        }
        .seller-post-image {
            width: 100%;
            height: 240px;
            object-fit: cover;
            margin: 12px 0 0 0;
            background: #f7f7f7;
            border-radius: 0;
            display: block;
        }
        .seller-post-content {
            font-size: 14px;
            color: #444;
            margin-bottom: 10px;
            margin-top: 12px;
            min-height: 48px;
        }
        .seller-post-stats {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            padding: 10px 0;
            border-top: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
            margin: 15px 0;
            font-size: 14px;
            color: #666;
        }
        .seller-post-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 3px;
            min-width: 50px;
        }
        .seller-post-stat-number {
            font-size: 15px;
            font-weight: bold;
            color: #227FCC;
        }
        .seller-post-stat-icon {
            font-size: 18px;
            color: #16CCC8;
        }
        /* كارت منشور البائع */
        .seller-post-card {
            border: 1.2px solid #e4e6ea;
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .seller-post-card:hover {
            border-color: #16CCC8;
            box-shadow: 0 4px 16px rgba(34,127,204,0.10);
        }
            overflow-x: hidden;
            max-width: 100%;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
            color: #333;
        }

        /* الشريط العلوي */
        .header {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 1000 !important;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100% !important;
        }

        .app-name {
            font-size: 40px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
            max-width: 600px;
            margin: 0 30px;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 12px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.15);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            outline: none;
            color: white;
            transition: all 0.3s ease;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
            font-weight: 500;
        }

        .search-input:focus {
            outline: none;
        }

        .search-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: white;
            font-size: 24px;
            padding: 10px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            position: relative;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        /* شريط التنقل */
        .nav-bar {
            background: white;
            position: fixed !important;
            top: 70px !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 999 !important;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            width: 100% !important;
        }

        .nav-items {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 25px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.3s;
            color: #495057;
            font-weight: 500;
            position: relative;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            color: #16CCC8;
            transform: translateY(-2px);
        }

        .nav-item:hover i {
            transform: scale(1.1);
            color: #16CCC8;
        }

        .nav-item i {
            font-size: 24px;
            transition: all 0.3s ease;
        }

        .nav-item.home-item {
            margin-left: 80px;
        }

        .nav-item.home-item i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item.home-item:hover i,
        .nav-item.home-item.active i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item.location-item {
            margin-right: auto;
            background: rgba(22, 204, 200, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(22, 204, 200, 0.3);
        }

        .nav-item.location-item:hover {
            background: rgba(22, 204, 200, 0.2);
            border-color: rgba(22, 204, 200, 0.5);
        }

        .nav-item.has-notification i {
            color: #ff0000 !important;
            -webkit-text-fill-color: #ff0000 !important;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 20px;
        }

        /* إخفاء شريط التمرير */
        #sellersStoriesContainer::-webkit-scrollbar,
        #liveStreamsContainer::-webkit-scrollbar,
        #premiumOffersContainer::-webkit-scrollbar {
            display: none;
        }

        /* تحسين تأثيرات القصص */
        .sellers-stories h3:hover {
            color: #16CCC8 !important;
            transition: all 0.3s ease;
        }

        /* تأثيرات الأسهم */
        #storiesLeftArrow:hover, #storiesRightArrow:hover {
            background: rgba(22, 204, 200, 1) !important;
            transform: translateY(-50%) scale(1.1) !important;
            box-shadow: 0 4px 12px rgba(22, 204, 200, 0.4) !important;
        }

        /* تأثيرات أسهم البث المباشر */
        #liveLeftArrow:hover, #liveRightArrow:hover {
            background: rgba(231, 76, 60, 1) !important;
            transform: translateY(-50%) scale(1.1) !important;
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.6) !important;
        }

        /* تأثير النبض لعلامة LIVE */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
    }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* تأثير الشيمر للعروض المميزة */
        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* منشورات البروفايل */
        .my-posts-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .post-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .post-card:hover {
            border: 2px solid #16CCC8;
            box-shadow: 0 8px 25px rgba(22, 204, 200, 0.15);
        }

        .post-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .post-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
        }

        .post-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .post-info h4 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .post-time {
            color: #666;
            font-size: 12px;
        }

        .post-content {
            color: #333;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .post-actions {
            display: flex;
            justify-content: space-around;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .post-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            font-size: 14px;
        }

        .post-action:hover {
            background: rgba(22, 204, 200, 0.1);
            color: #16CCC8;
        }

        .post-action i {
            font-size: 16px;
        }



        /* شريط القصص */
        .stories-section {
            margin-bottom: 20px;
            position: relative;
        }

        .stories-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }

        .stories-wrapper {
            display: flex;
            gap: 15px;
            transition: transform 1s ease;
            width: fit-content;
        }

        .story-card {
            width: 120px;
            height: 180px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            cursor: pointer;
            background: white;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            position: relative;
        }

        .story-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .story-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            color: white;
            font-size: 24px;
        }

        .story-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .story-name {
            height: 60px;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: 600;
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
        }

        /* أسهم التنقل */
        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            z-index: 10;
            transition: all 0.3s;
        }

        .nav-arrow:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%);
        }

        .nav-arrow.prev {
            right: 10px;
        }

        .nav-arrow.next {
            left: 10px;
        }
        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            position: relative;
        }

        /* القائمة المنسدلة الرئيسية */
        .main-menu-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            min-width: 250px;
            max-height: 400px;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateX(20px);
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        /* إخفاء شريط التمرير */
        .main-menu-dropdown {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE 10+ */
        }
        .main-menu-dropdown::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .main-menu-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: #4a4a4a;
            cursor: pointer;
            transition: color 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
        }

        .menu-item:hover {
            color: #16CCC8;
        }

        .menu-item i {
            font-size: 16px;
            width: 20px;
            text-align: center;
            transition: color 0.3s ease;
        }

        .menu-item:hover i {
            color: #16CCC8;
        }

        .menu-item.logout {
            color: #e74c3c;
        }

        .menu-item.logout:hover {
            color: #c0392b;
        }

        .menu-item.logout:hover i {
            color: #c0392b;
        }

        .menu-divider {
            height: 1px;
            background: rgba(0, 0, 0, 0.1);
            margin: 8px 0;
        }

        /* تنسيق قائمة اختيار الدولة */
        .country-selector {
            font-family: 'Cairo', sans-serif;
        }

        .country-selector #countryOptions {
            scrollbar-width: thin;
            scrollbar-color: #16CCC8 #f8f9fa;
            border: 2px solid #e4e6ea !important;
            background: white !important;
        }

        .country-selector #countryOptions::-webkit-scrollbar {
            width: 8px;
        }

        .country-selector #countryOptions::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 10px;
            margin: 5px;
        }

        .country-selector #countryOptions::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            border-radius: 10px;
            border: 2px solid #f8f9fa;
        }

        .country-selector #countryOptions::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #227FCC, #16CCC8);
        }

        /* تأثير الانتقال للقائمة */
        .country-selector #countryOptions {
            animation: slideDown 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-15px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* تحسين مظهر المستطيل الرئيسي */
        .country-selector #countryDropdown:hover {
            border-color: #16CCC8 !important;
            box-shadow: 0 4px 15px rgba(22, 204, 200, 0.2) !important;
        }
    </style>
    <script>
    // Dropdown menu UI toggle only
    function toggleMainMenu() {
      var menu = document.getElementById('mainMenuDropdown');
      if (menu) {
        menu.classList.toggle('show');
      }
    }
    // Optional: close menu when clicking outside
    document.addEventListener('click', function(event) {
      var menu = document.getElementById('mainMenuDropdown');
      var icon = document.querySelector('.menu-icon');
      if (menu && icon && !icon.contains(event.target)) {
        menu.classList.remove('show');
      }
    });
    </script>
</head>
<body dir="rtl">
    
 <!-- الشريط العلوي -->
<div class="header">
    <div class="user-section">
        <div class="menu-icon" onclick="toggleMainMenu()">
            <i class="fas fa-bars"></i>
            <!-- القائمة المنسدلة -->
            <div class="main-menu-dropdown" id="mainMenuDropdown">
                <div class="menu-item">
                    <i class="fas fa-moon"></i>
                    <span>الوضع الليلي</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-sun"></i>
                    <span>الوضع النهاري</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-palette"></i>
                    <span>تخصيص الوضع الليلي</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bell"></i>
                    <span>إعدادات الإشعارات</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-globe"></i>
                    <span>تغيير اللغة</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>الخصوصية والأمان</span>
                </div>
                <div class="menu-divider"></div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>إحصائيات الملف الشخصي</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-dollar-sign"></i>
                    <span>تقرير الأرباح</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-receipt"></i>
                    <span>سجل المعاملات</span>
                </div>
                <div class="menu-divider"></div>
                <div class="menu-item">
                    <i class="fas fa-crown"></i>
                    <span>الحساب المميز</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-palette"></i>
                    <span>تخصيص الملف الشخصي</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-sync"></i>
                    <span>مزامنة البيانات</span>
                </div>
                <div class="menu-divider"></div>
                <div class="menu-item">
                    <i class="fas fa-question-circle"></i>
                    <span>مركز المساعدة</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-headset"></i>
                    <span>اتصل بنا</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-file-contract"></i>
                    <span>الشروط والأحكام</span>
                </div>
                <div class="menu-divider"></div>
                <div class="menu-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </div>
            </div>
        </div>
        <div class="user-avatar">
            <img src="HusseinNihad.png" alt="المستخدم" onerror="this.style.display='none'">
        </div>
        <div class="user-name">حسين نهاد</div>
    </div>
    <div class="search-section">
        <input type="text" class="search-input" placeholder="ابحث عن خدمة أو منتج...">
        <button class="search-btn">
            <i class="fas fa-search"></i>
        </button>
    </div>
    <div class="app-name">Get Me</div>
</div>


    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item home-item active">
              <a href="Home.html">
  <i class="fas fa-home"></i>
</a>

                <span>الرئيسية</span>
            </div>
            <div class="nav-item has-notification">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge">3</div>
            </div>
            <div class="nav-item">
                <i class="fas fa-comments"></i>
                <span>الرسائل</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
            <div class="nav-item location-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>بغداد، العراق</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- عنوان القصص -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-circle" style="color: #16CCC8; margin-left: 8px;"></i>
                قصص البائعين
            </h3>
        </div>

        <!-- شريط القصص للبائعين -->
        <div class="sellers-stories" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <!-- زر إنشاء قصة داخل الشريط من اليمين -->
            <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;">
                <button onclick="createNewStory()" style="
                    background: linear-gradient(45deg, #16CCC8, #227FCC);
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(22, 204, 200, 0.3);
                " onmouseover="
                    this.style.transform='translateY(-2px)';
                    this.style.boxShadow='0 4px 12px rgba(22, 204, 200, 0.5)';
                " onmouseout="
                    this.style.transform='translateY(0)';
                    this.style.boxShadow='0 2px 8px rgba(22, 204, 200, 0.3)';
                ">
                    <i class="fas fa-plus" style="margin-left: 5px;"></i>
                    إنشاء قصة جديدة
                </button>
            </div>
            <div style="position: relative;">
                <!-- سهم التمرير الأيسر -->
                <button id="storiesLeftArrow" onclick="scrollStories('left')" style="
                    position: absolute;
                    left: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="storiesRightArrow" onclick="scrollStories('right')" style="
                    position: absolute;
                    right: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="sellersStoriesContainer" style="
                    display: flex;
                    gap: 15px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 10px 15px;
                    height: 95px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                    align-items: center;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- عنوان البث المباشر -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-video" style="color: #e74c3c; margin-left: 8px;"></i>
                البث المباشر
            </h3>
        </div>

        <!-- شريط البث المباشر -->
        <div class="live-streams" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <!-- زر بدء بث مباشر داخل الشريط من اليمين -->
            <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;">
                <button onclick="startLiveStream()" style="
                    background: linear-gradient(45deg, #8e44ad, #e74c3c, #f39c12);
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
                " onmouseover="
                    this.style.transform='translateY(-2px)';
                    this.style.boxShadow='0 4px 12px rgba(231, 76, 60, 0.5)';
                " onmouseout="
                    this.style.transform='translateY(0)';
                    this.style.boxShadow='0 2px 8px rgba(231, 76, 60, 0.3)';
                ">
                    <i class="fas fa-video" style="margin-left: 5px;"></i>
                    بدء بث مباشر
                </button>
            </div>
            <div style="position: relative;">
                <!-- سهم التمرير الأيسر -->
                <button id="liveLeftArrow" onclick="scrollLiveStreams('left')" style="
                    position: absolute;
                    left: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(231, 76, 60, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="liveRightArrow" onclick="scrollLiveStreams('right')" style="
                    position: absolute;
                    right: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(231, 76, 60, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="liveStreamsContainer" style="
                    display: flex;
                    gap: 15px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 10px 15px;
                    height: 95px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                    align-items: center;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- عنوان العروض المميزة -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-star" style="color: #f39c12; margin-left: 8px;"></i>
                العروض المميزة - عقارات
            </h3>
        </div>

        <!-- شريط العروض المميزة -->
        <div class="premium-offers-section" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <div class="premium-offers-container" style="
                position: relative;
                overflow: hidden;
                padding: 10px 0;
            ">
                <!-- سهم التمرير الأيسر -->
                <button id="premiumLeftArrow" onclick="scrollPremiumOffers('left')" style="
                    position: absolute;
                    left: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 45px;
                    height: 45px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="premiumRightArrow" onclick="scrollPremiumOffers('right')" style="
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 45px;
                    height: 45px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="premiumOffersContainer" style="
                    display: flex;
                    gap: 20px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 0 60px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- قسم المحتوى السفلي -->
    <div style="display: flex; gap: 25px; align-items: flex-start; flex-direction: row;">
            <!-- مستطيل العروض الجانبية (تحليل السوق، الأسعار، المشاريع) في الجهة اليسرى -->
            <div style="width: 420px; max-width: 100%; display: flex; flex-direction: column; gap: 18px; order: 2;">
                <div id="marketAnalysisWidget" style="position: relative; transition: all 0.3s;">
                    <!-- زر تكبير/تصغير أعلى يمين النافذة -->
                    <span title="تكبير/تصغير" id="marketAnalysisFullscreenBtn" style="position: absolute; top: 18px; right: 22px; cursor: pointer; color: #888; font-size: 20px; z-index: 20; display: flex; align-items: center; transition: color 0.2s; background: #fff; border-radius: 50%; padding: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.07);" onclick="toggleMarketAnalysisFullscreen()">
                        <i id="marketAnalysisFullscreenIcon" class="fas fa-expand"></i>
                    </span>
                    <div style="background: white; border-radius: 20px; padding: 24px; margin-bottom: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.10); border: 1px solid #e4e6ea; width: 100%;">
                        <div style="text-align: center; margin-bottom: 12px; font-size: 15px;">
                            <h4 style="margin: 0; color: #333; font-size: 22px; font-weight: bold;">
                                <i class="fas fa-chart-line" style="color: #16CCC8; margin-left: 10px; font-size: 24px;"></i>
                                تحليل السوق العقاري
                            </h4>
                        </div>
                        <!-- قائمة اختيار الدولة -->
                        <div style="text-align: center; margin-bottom: 18px;">
                            <div class="country-selector" style="position: relative; display: inline-block; width: 200px;">
                                <div id="countryDropdown" onclick="toggleCountryDropdown()" style="
                                    background: white;
                                    border: 2px solid #e4e6ea;
                                    border-radius: 15px;
                                    padding: 12px 15px;
                                    cursor: pointer;
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    transition: all 0.3s ease;
                                    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                                " onmouseover="this.style.borderColor='#16CCC8'" onmouseout="this.style.borderColor='#e4e6ea'">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span id="selectedFlag" style="font-size: 20px; width: 25px; text-align: center;">🇮🇶</span>
                                        <span id="selectedCountry" style="font-weight: 600; color: #333;">العراق</span>
                                    </div>
                                    <i class="fas fa-chevron-down" id="dropdownArrow" style="color: #666; transition: transform 0.3s ease;"></i>
                                </div>
                                <div id="countryOptions" style="
                                    position: absolute;
                                    top: 100%;
                                    left: 0;
                                    right: 0;
                                    background: white;
                                    border: 2px solid #e4e6ea;
                                    border-radius: 15px;
                                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                                    z-index: 1000;
                                    max-height: 250px;
                                    overflow-y: auto;
                                    display: none;
                                    margin-top: 8px;
                                    scrollbar-width: none;
                                    -ms-overflow-style: none;
                                ">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                        <div style="text-align: center; margin-bottom: 18px;">
                            <div id="marketChartTabs" style="position: relative; display: inline-flex; background: #f8f9fa; border-radius: 25px; padding: 5px; border: 1px solid #e4e6ea; box-shadow: 0 2px 8px rgba(0,0,0,0.05); min-width: 240px;">
                                <button id="weeklyBtn" onclick="updateMarketChartTab('weekly')" style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px; transition: background 0.2s, color 0.2s;">أسبوعي</button>
                                <button id="monthlyBtn" onclick="updateMarketChartTab('monthly')" style="background: transparent; border: none; color: #666; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px; transition: background 0.2s, color 0.2s;">شهري</button>
                                <button id="yearlyBtn" onclick="updateMarketChartTab('yearly')" style="background: transparent; border: none; color: #666; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px; transition: background 0.2s, color 0.2s;">سنوي</button>

                            </div>
                        </div>
                        <div style="background: #fff; border: 1px solid #e4e6ea; border-radius: 10px; padding: 32px 24px 24px 24px; margin-bottom: 32px; box-shadow: 0 8px 32px rgba(34,127,204,0.13); min-height: 260px; display: flex; align-items: center; justify-content: center;">
                            <canvas id="marketChart" height="160" style="width: 100% !important; max-width: 100%;"></canvas>
                        </div>
                        <div id="marketAnalysisStats" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                            <div style="background: #fff; border: 1px solid #e4e6ea; box-shadow: 0 4px 18px rgba(34,127,204,0.10); padding: 12px; border-radius: 10px; text-align: center;">
                                <div style="color: #27ae60; font-size: 18px; margin-bottom: 4px;"><i class="fas fa-home"></i></div>
                                <div style="color: #27ae60; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+8%</div>
                                <div style="color: #666; font-size: 10px;">الشقق</div>
                            </div>
                            <div style="background: #fff; border: 1px solid #e4e6ea; box-shadow: 0 4px 18px rgba(34,127,204,0.10); padding: 12px; border-radius: 10px; text-align: center;">
                                <div style="color: #f39c12; font-size: 18px; margin-bottom: 4px;"><i class="fas fa-map-marked-alt"></i></div>
                                <div style="color: #f39c12; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+15%</div>
                                <div style="color: #666; font-size: 10px;">الأراضي</div>
                            </div>
                            <div style="background: #fff; border: 1px solid #e4e6ea; box-shadow: 0 4px 18px rgba(34,127,204,0.10); padding: 12px; border-radius: 10px; text-align: center;">
                                <div style="color: #e67e22; font-size: 18px; margin-bottom: 4px;"><i class="fas fa-building"></i></div>
                                <div style="color: #e67e22; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+5%</div>
                                <div style="color: #666; font-size: 10px;">التجاري</div>
                            </div>
                        </div>
                        <!-- واجهة اختيار وتحليل مخصص -->
                        <div id="marketCustomAnalysis" style="margin: 38px auto 0 auto; max-width: 370px; display: flex; flex-direction: column; gap: 18px; align-items: stretch;">
                            <!-- اختيار الدولة -->
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div id="countrySelectBox" style="width: 200px; background: #fff; border: 1.5px solid #e4e6ea; border-radius: 12px; min-height: 38px; display: flex; align-items: center; justify-content: space-between; padding: 0 14px; cursor: pointer; position: relative; box-shadow: 0 2px 8px rgba(34,127,204,0.07); font-size: 15px;">
                                    <span id="selectedCountryName" style="display: flex; align-items: center; gap: 8px;">اختر الدولة</span>
                                    <span style="font-size: 18px; color: #16CCC8;"><i class="fas fa-chevron-down"></i></span>
                                    <div id="countryDropdown" style="display: none; position: absolute; top: 110%; left: 0; right: 0; background: #fff; border: 1px solid #e4e6ea; border-radius: 10px; max-height: 180px; overflow-y: auto; z-index: 100; box-shadow: 0 4px 18px rgba(34,127,204,0.10);">
                                        <!-- قائمة الدول -->
                                    </div>
                                </div>
                            </div>
                            <!-- المحافظة/القضاء -->
                            <input id="provinceInput" type="text" placeholder="المحافظة أو القضاء" style="background: #fff; border: 1.5px solid #e4e6ea; border-radius: 12px; min-height: 48px; font-size: 15px; padding: 0 14px; box-shadow: 0 2px 8px rgba(34,127,204,0.07); outline: none;">
                            <!-- المنطقة -->
                            <input id="areaInput" type="text" placeholder="اسم المنطقة" style="background: #fff; border: 1.5px solid #e4e6ea; border-radius: 12px; min-height: 48px; font-size: 15px; padding: 0 14px; box-shadow: 0 2px 8px rgba(34,127,204,0.07); outline: none;">
                            <!-- نوع العقار -->
                            <div style="display: flex; gap: 32px; justify-content: center; margin: 8px 0 0 0;">
                                <label style="display: flex; align-items: center; gap: 7px; cursor: pointer; font-size: 15px;">
                                    <span>سكني</span>
                                    <span class="type-radio" data-type="سكني" style="width: 22px; height: 22px; border-radius: 50%; border: 2.5px solid #b0b0b0; display: inline-block; background: #fff; transition: background 0.2s; box-shadow: 0 1px 4px rgba(34,127,204,0.10);"></span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 7px; cursor: pointer; font-size: 15px;">
                                    <span>تجاري</span>
                                    <span class="type-radio" data-type="تجاري" style="width: 22px; height: 22px; border-radius: 50%; border: 2.5px solid #b0b0b0; display: inline-block; background: #fff; transition: background 0.2s; box-shadow: 0 1px 4px rgba(34,127,204,0.10);"></span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 7px; cursor: pointer; font-size: 15px;">
                                    <span>استثماري</span>
                                    <span class="type-radio" data-type="استثماري" style="width: 22px; height: 22px; border-radius: 50%; border: 2.5px solid #b0b0b0; display: inline-block; background: #fff; transition: background 0.2s; box-shadow: 0 1px 4px rgba(34,127,204,0.10);"></span>
                                </label>
                            </div>
                            <!-- زر تحقق -->
                            <button id="analyzeBtn" style="margin: 8px auto 0 auto; display: block; background: linear-gradient(90deg, #227FCC, #16CCC8); color: #fff; font-size: 14px; font-family: 'Cairo', sans-serif; font-weight: bold; border: none; border-radius: 20px; padding: 7px 0; width: 48%; box-shadow: 0 2px 8px rgba(34,127,204,0.13); letter-spacing: 1px; cursor: pointer; transition: background 0.2s;">تحقق</button>
                        </div>

                    </div>
                </div>
                <div id="realEstatePricesWidget">
                    <div style="background: white; border-radius: 15px; padding: 28px 18px 24px 18px; margin-bottom: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.10); width: 100%; min-height: 520px; display: flex; flex-direction: column;">
                        <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">
                            <i class="fas fa-map-marked-alt" style="color: #e74c3c; margin-left: 8px;"></i>
                            أسعار العقارات - هذا الأسبوع
                        </h4>
                        <div id="pricesCountryFilter" style="margin-bottom: 18px; display: flex; flex-direction: column; align-items: flex-start; gap: 10px;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div id="pricesCountrySelectBox" style="width: 200px; background: #fff; border: 1.5px solid #e4e6ea; border-radius: 12px; min-height: 38px; display: flex; align-items: center; justify-content: space-between; padding: 0 14px; cursor: pointer; position: relative; box-shadow: 0 2px 8px rgba(34,127,204,0.07); font-size: 15px;">
                                    <span id="pricesSelectedCountryName" style="display: flex; align-items: center; gap: 8px;">اختر الدولة</span>
                                    <span style="font-size: 18px; color: #16CCC8;"><i class="fas fa-chevron-down"></i></span>
                                    <div id="pricesCountryDropdown" style="display: none; position: absolute; top: 110%; left: 0; right: 0; background: #fff; border: 1px solid #e4e6ea; border-radius: 10px; max-height: 180px; overflow-y: auto; z-index: 100; box-shadow: 0 4px 18px rgba(34,127,204,0.10);">
                                        <!-- قائمة الدول -->
                                    </div>
                                </div>
                            </div>
                            <div id="geoChoices" style="display: flex; gap: 10px; width: 100%; margin: 16px 0 0 0; justify-content: stretch;">
                                <span class="geo-choice-btn" data-geo="province" style="flex:1; color: #888; font-size: 13px; font-family: 'Cairo',sans-serif; cursor: pointer; text-align: center; padding: 0 2px; transition: color 0.2s;">المحافظة</span>
                                <span class="geo-choice-btn" data-geo="district" style="flex:1; color: #888; font-size: 13px; font-family: 'Cairo',sans-serif; cursor: pointer; text-align: center; padding: 0 2px; transition: color 0.2s;">القضاء</span>
                                <span class="geo-choice-btn" data-geo="subdistrict" style="flex:1; color: #888; font-size: 13px; font-family: 'Cairo',sans-serif; cursor: pointer; text-align: center; padding: 0 2px; transition: color 0.2s;">الناحية</span>
                                <span class="geo-choice-btn" data-geo="area" style="flex:1; color: #888; font-size: 13px; font-family: 'Cairo',sans-serif; cursor: pointer; text-align: center; padding: 0 2px; transition: color 0.2s;">المنطقة</span>
                            </div>
                            <div id="geoListContainer" style="margin: 18px 0 0 0; min-height: 38px;"></div>
                            <!-- النتائج ستظهر تحت زر التحقق مباشرة (انظر الكود البرمجي) -->
                            <div style="display: flex; gap: 32px; justify-content: center; margin: 18px 0 0 0; width: 100%;">
                                <label style="display: flex; align-items: center; gap: 7px; cursor: pointer; font-size: 15px;">
                                    <span>سكني</span>
                                    <span class="prices-type-radio" data-type="سكني" style="width: 22px; height: 22px; border-radius: 50%; border: 2.5px solid #b0b0b0; display: inline-block; background: #fff; transition: background 0.2s; box-shadow: 0 1px 4px rgba(34,127,204,0.10);"></span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 7px; cursor: pointer; font-size: 15px;">
                                    <span>تجاري</span>
                                    <span class="prices-type-radio" data-type="تجاري" style="width: 22px; height: 22px; border-radius: 50%; border: 2.5px solid #b0b0b0; display: inline-block; background: #fff; transition: background 0.2s; box-shadow: 0 1px 4px rgba(34,127,204,0.10);"></span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 7px; cursor: pointer; font-size: 15px;">
                                    <span>استثماري</span>
                                    <span class="prices-type-radio" data-type="استثماري" style="width: 22px; height: 22px; border-radius: 50%; border: 2.5px solid #b0b0b0; display: inline-block; background: #fff; transition: background 0.2s; box-shadow: 0 1px 4px rgba(34,127,204,0.10);"></span>
                                </label>
                            </div>
                            <button id="pricesVerifyBtn" style="margin: 14px auto 0 auto; display: block; background: linear-gradient(90deg, #227FCC, #16CCC8); color: #fff; font-size: 14px; font-family: 'Cairo', sans-serif; font-weight: bold; border: none; border-radius: 20px; padding: 7px 0; width: 48%; box-shadow: 0 2px 8px rgba(34,127,204,0.13); letter-spacing: 1px; cursor: pointer; transition: background 0.2s; align-self: center;">تحقق</button>
                            <div id="pricesResultContainer" style="margin: 32px auto 0 auto; min-height: 60px; background: #f8f9fa; border-radius: 14px; box-shadow: 0 2px 8px rgba(34,127,204,0.07); padding: 18px 12px; max-width: 420px; text-align: center;"></div>
                        </div>
                        <!-- لا تعرض أي نتائج هنا إطلاقاً -->
                        <div id="pricesResultContainer"></div>
                    </div>
                </div>
                <div id="govProjectsWidget">
                    <div style="background: white; border-radius: 15px; padding: 18px; box-shadow: 0 4px 15px rgba(0,0,0,0.10); width: 100%;">
                        <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">
                            <i class="fas fa-building" style="color: #f39c12; margin-left: 8px;"></i>
                            المشاريع الحكومية
                        </h4>
                        <div style="background: linear-gradient(135deg, #227FCC, #16CCC8); color: white; padding: 10px; border-radius: 10px; text-align: center; margin-bottom: 10px;">
                            <div style="font-size: 16px; font-weight: bold; margin-bottom: 3px;">
                                <i class="fas fa-handshake"></i>
                                فرص استثمارية
                            </div>
                            <div style="font-size: 13px; opacity: 0.9;">مشاريع تبحث عن مستثمرين</div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 10px; margin-bottom: 10px; background: #fafafa;">
                            <h5 style="margin: 0 0 5px 0; color: #333; font-size: 13px;">مشروع المدينة الذكية - بغداد</h5>
                            <p style="margin: 0 0 7px 0; color: #666; font-size: 11px; line-height: 1.4;">مشروع سكني متكامل يحتاج مستثمرين ومقاولين للبناء والتطوير</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 12px;">500 مليون دولار</div>
                                <button onclick="showProjectDetails('مشروع المدينة الذكية - بغداد', 'مشروع سكني متكامل يحتاج مستثمرين ومقاولين للبناء والتطوير', '500 مليون دولار')" style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 4px 10px; border-radius: 15px; cursor: pointer; font-size: 10px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 10px; margin-bottom: 10px; background: #fafafa;">
                            <h5 style="margin: 0 0 5px 0; color: #333; font-size: 13px;">مجمع تجاري - البصرة</h5>
                            <p style="margin: 0 0 7px 0; color: #666; font-size: 11px; line-height: 1.4;">مجمع تجاري كبير في منطقة استراتيجية يبحث عن شركاء استثماريين</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 12px;">200 مليون دولار</div>
                                <button onclick="showProjectDetails('مجمع تجاري - البصرة', 'مجمع تجاري كبير في منطقة استراتيجية يبحث عن شركاء استثماريين', '200 مليون دولار')" style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 4px 10px; border-radius: 15px; cursor: pointer; font-size: 10px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 10px; margin-bottom: 10px; background: #fafafa;">
                            <h5 style="margin: 0 0 5px 0; color: #333; font-size: 13px;">مشروع الإسكان الشعبي</h5>
                            <p style="margin: 0 0 7px 0; color: #666; font-size: 11px; line-height: 1.4;">بناء وحدات سكنية للطبقة المتوسطة في عدة محافظات</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 12px;">300 مليون دولار</div>
                                <button onclick="showProjectDetails('مشروع الإسكان الشعبي', 'بناء وحدات سكنية للطبقة المتوسطة في عدة محافظات', '300 مليون دولار')" style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 4px 10px; border-radius: 15px; cursor: pointer; font-size: 10px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <!-- تم حذف مشروع تطوير المنطقة الصناعية بناءً على طلب المستخدم -->
                    </div>
                </div>
            </div>
            <!-- مستطيل منشورات البائعين (شبكة 2 عمود) في الجهة اليمنى -->
            <div style="flex: 2; background: white; border-radius: 18px; padding: 28px 18px; box-shadow: 0 6px 20px rgba(0,0,0,0.10); margin-bottom: 0; order: 0;">
                <div style="margin-bottom: 22px;">
                    <h3 style="margin: 0; color: #333; font-size: 20px;">
                        <i class="fas fa-newspaper" style="color: #16CCC8; margin-left: 8px;"></i>
                        منشورات البائعين
                    </h3>
                </div>
                <div id="sellersPostsContainer" class="my-posts-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 22px; font-size: 18px;">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
        </div>
                    <div style="width: 15px; height: 45%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 90%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 70%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 85%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 95%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                </div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 10px; color: #666;">
                <span>السبت</span><span>الأحد</span><span>الاثنين</span><span>الثلاثاء</span><span>الأربعاء</span><span>الخميس</span><span>الجمعة</span>
            </div>
        </div>

        <!-- مؤشر الأداء الرئيسي -->
        <div style="
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 15px;
        ">
            <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">+12%</div>
            <div style="font-size: 14px; opacity: 0.9;">هذا الأسبوع</div>
            <div style="font-size: 12px; opacity: 0.8; margin-top: 3px;">📈 نمو مستمر</div>
        </div>

        <!-- الإحصائيات التفصيلية -->
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px;">
            <div style="
                background: #e8f5e8;
                border: 1px solid #c3e6c3;
                padding: 12px;
                border-radius: 10px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
            " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                <div style="color: #27ae60; font-size: 18px; margin-bottom: 4px;">
                    <i class="fas fa-home"></i>
                </div>
                <div style="color: #27ae60; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+8%</div>
                <div style="color: #666; font-size: 10px;">الشقق</div>
            </div>

            <div style="
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 12px;
                border-radius: 10px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
            " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                <div style="color: #e67e22; font-size: 18px; margin-bottom: 4px;">
                    <i class="fas fa-building"></i>
                </div>
                <div style="color: #e67e22; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+5%</div>
                <div style="color: #666; font-size: 10px;">التجاري</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="./geoData.js"></script>
    <script>
    // تحقق من تحميل geoData
    if (!window.geoData || Object.keys(window.geoData).length === 0) {
        alert('تعذر تحميل بيانات الدول (geoData.js). لن تظهر قائمة الدول أو الأعلام. تأكد من وجود الملف وعدم وجود أخطاء في الكونسول.');
    }
    </script>
    <script>
        // تعريف قائمة الدول من geoData.js تلقائياً
        function getCountryFlag(name) {
            // Unicode emoji flags for common Arab countries
            const flagMap = {
                'العراق': '🇮🇶',
                'مصر': '🇪🇬',
                'السعودية': '🇸🇦',
                'الإمارات': '🇦🇪',
                'سوريا': '🇸🇾',
                'لبنان': '🇱🇧',
                'الأردن': '🇯🇴',
                'تونس': '🇹🇳',
                'الجزائر': '🇩🇿',
                'المغرب': '🇲🇦',
                'فلسطين': '🇵🇸',
                'ليبيا': '🇱🇾',
                'اليمن': '🇾🇪',
                'الكويت': '🇰🇼',
                'قطر': '🇶🇦',
                'البحرين': '🇧🇭',
                'عمان': '🇴🇲',
                'تركيا': '🇹🇷',
                'فرنسا': '🇫🇷',
                'ألمانيا': '🇩🇪',
                'إيطاليا': '🇮🇹',
                'إسبانيا': '🇪🇸',
                'بريطانيا': '🇬🇧',
                'الولايات المتحدة': '🇺🇸',
                'روسيا': '🇷🇺',
                'الصين': '🇨🇳',
                'الهند': '🇮🇳',
                'اليونان': '🇬🇷',
                'قبرص': '🇨🇾',
                'رومانيا': '🇷🇴',
                'بلغاريا': '🇧🇬',
                'أوكرانيا': '🇺🇦',
                'بولندا': '🇵🇱',
                'هولندا': '🇳🇱',
                'السويد': '🇸🇪',
                'النرويج': '🇳🇴',
                'سويسرا': '🇨🇭',
                'النمسا': '🇦🇹',
                'بلجيكا': '🇧🇪',
                'البرتغال': '🇵🇹',
                'الدنمارك': '🇩🇰',
                'فنلندا': '🇫🇮',
                'المجر': '🇭🇺',
                'التشيك': '🇨🇿',
                'سلوفاكيا': '🇸🇰',
                'كندا': '🇨🇦',
                'أستراليا': '🇦🇺',
                'البرازيل': '🇧🇷',
                'الأرجنتين': '🇦🇷',
                'المكسيك': '🇲🇽',
                'جنوب أفريقيا': '🇿🇦',
            };
            return flagMap[name] || '🌐';
        }
        function getAllCountriesFromGeoData() {
            if (!window.geoData) return [];
            return Object.keys(window.geoData).map(function(name) {
                return { name: name, flag: getCountryFlag(name) };
            });
        }
        const countries = getAllCountriesFromGeoData();
        // رسم وتحكم رسم السوق العقاري

        // خاصية تكبير/تصغير نافذة تحليل السوق (نفس منطق منشورات البائعين)
        window.toggleMarketAnalysisFullscreen = function() {
            var widget = document.getElementById('marketAnalysisWidget');
            var icon = document.getElementById('marketAnalysisFullscreenIcon');
            if (!widget.classList.contains('fullscreen')) {
                widget.classList.add('fullscreen');
                widget.style.position = 'fixed';
                widget.style.top = '0';
                widget.style.left = '0';
                widget.style.transform = 'none';
                widget.style.zIndex = '9999';
                widget.style.width = '100vw';
                widget.style.height = '100vh';
                widget.style.maxWidth = '100vw';
                widget.style.maxHeight = '100vh';
                widget.style.overflowY = 'auto';
                widget.style.background = '#fff';
                widget.style.borderRadius = '0';
                widget.style.boxShadow = '0 8px 40px rgba(0,0,0,0.18)';
                if (icon) { icon.classList.remove('fa-expand'); icon.classList.add('fa-compress'); }
                // إبقاء زر التكبير ظاهر دائماً
                var btn = document.getElementById('marketAnalysisFullscreenBtn');
                if (btn) {
                    btn.style.display = 'flex';
                    btn.style.position = 'fixed';
                    btn.style.top = '24px';
                    btn.style.right = '32px';
                    btn.style.zIndex = '10001';
                    if (btn.parentElement !== widget) {
                        widget.insertBefore(btn, widget.firstChild);
                    }
                }
            } else {
                widget.classList.remove('fullscreen');
                widget.style.position = '';
                widget.style.top = '';
                widget.style.left = '';
                widget.style.transform = '';
                widget.style.zIndex = '';
                widget.style.width = '';
                widget.style.height = '';
                widget.style.maxWidth = '';
                widget.style.maxHeight = '';
                widget.style.overflowY = '';
                widget.style.background = '';
                widget.style.borderRadius = '';
                widget.style.boxShadow = '';
                if (icon) { icon.classList.remove('fa-compress'); icon.classList.add('fa-expand'); }
                // إبقاء زر التكبير ظاهر دائماً
                var btn = document.getElementById('marketAnalysisFullscreenBtn');
                if (btn) {
                    btn.style.display = 'flex';
                    btn.style.position = 'absolute';
                    btn.style.top = '18px';
                    btn.style.right = '22px';
                    btn.style.zIndex = '20';
                    if (btn.parentElement !== widget) {
                        widget.insertBefore(btn, widget.firstChild);
                    }
                }
            }
        }

        // دالة تحميل قصص البائعين
        function loadSellersStories(serviceName) {
            const container = document.getElementById('sellersStoriesContainer');
            const stories = generateSellersStories(serviceName);
            container.innerHTML = stories.map(story => `
                <div style="min-width: 75px; text-align: center; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                    <div style="width: 65px; height: 65px; border-radius: 50%; background: linear-gradient(45deg, #16CCC8, #227FCC); padding: 3px; margin: 0 auto 8px; box-shadow: 0 4px 15px rgba(22, 204, 200, 0.3); transition: all 0.3s ease;">
                        <div style="width: 100%; height: 100%; border-radius: 50%; background: white; display: flex; align-items: center; justify-content: center; overflow: hidden;">
                            <img src="${story.avatar}" alt="${story.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%; transition: all 0.3s ease;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; background: linear-gradient(45deg, #16CCC8, #227FCC); color: white; font-size: 20px; border-radius: 50%;"><i class='${story.icon}'></i></div>
                        </div>
                    </div>
                    <div style="font-size: 11px; color: #333; font-weight: 600; line-height: 1.2; max-width: 75px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        ${story.name.length > 8 ? story.name.substring(0, 8) + '...' : story.name}
                    </div>
                </div>
            `).join('');
        }
        let marketChart;
        const marketChartData = {
            weekly: {
                labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                data: [10, 18, 14, 22, 30, 25, 20]
            },
            monthly: {
                labels: ['يناير', 'فبراير', 'مارس', 'ابريل', 'مايو', 'يونيو', 'يوليو', 'اغسطس', 'سبتمبر', 'اكتوبر', 'نوفمبر', 'ديسمبر'],
                data: [12, 15, 18, 22, 28, 35, 40, 38, 32, 28, 20, 15]
            },
            yearly: {
                labels: ['2021', '2022', '2023', '2024', '2025'],
                data: [120, 150, 180, 210, 250]
            }
        };

        function drawMarketChart(type = 'weekly') {
            const ctx = document.getElementById('marketChart').getContext('2d');
            if (marketChart) marketChart.destroy();
            marketChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: marketChartData[type].labels,
                    datasets: [{
                        label: '',
                        data: marketChartData[type].data,
                        fill: true,
                        borderColor: '#227FCC',
                        backgroundColor: 'rgba(34,127,204,0.18)',
                        tension: 0.45,
                        pointRadius: 2,
                        pointBackgroundColor: '#16CCC8',
                        borderWidth: 1.2
                    }]
                },
                options: {
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { 
                            grid: { display: false }, 
                            ticks: { 
                                font: { family: 'Cairo', size: 11 }, 
                                maxRotation: 0, 
                                minRotation: 0 
                            } 
                        },
                        y: { 
                            beginAtZero: true, 
                            grid: { color: '#e4e6ea' }, 
                            ticks: { font: { family: 'Cairo', size: 13 } },
                            suggestedMax: 50
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }


        // تحديث الرسم البياني وتبويب المؤشر
        function updateMarketChartTab(type) {
            drawMarketChart(type);
            // تحديث ألوان الأزرار
            var tabs = ['weekly', 'monthly', 'yearly'];
            tabs.forEach(function(tab) {
                var btn = document.getElementById(tab + 'Btn');
                if (btn) {
                    if (tab === type) {
                        btn.style.background = 'linear-gradient(135deg, #16CCC8, #227FCC)';
                        btn.style.color = 'white';
                    } else {
                        btn.style.background = 'transparent';
                        btn.style.color = '#666';
                    }
                }
            });
        }

        // منطق واجهة التحليل المخصص
            // تم تعطيل عرض النتائج في pricesListContainer نهائياً. جميع النتائج تظهر فقط في pricesResultContainer تحت زر التحقق.
            // تأكد من تعريف المتغير قبل استخدامه
            var pricesCountryDropdown = document.getElementById('pricesCountryDropdown');
            if (pricesCountryDropdown) {
                pricesCountryDropdown.style.display = 'none';
            }
                        // نهاية منطق واجهة التحليل المخصص
            // ...existing code...
            // منطق اختيار نوع العقار (دائرة)
            var pricesTypeRadios = document.querySelectorAll('.prices-type-radio');
            pricesTypeRadios.forEach(function(radio) {
                radio.onclick = function() {
                    pricesTypeRadios.forEach(function(r) {
                        r.style.background = '#fff';
                        r.style.borderColor = '#b0b0b0';
                    });
                    this.style.background = 'radial-gradient(circle at 60% 40%, #16CCC8 60%, #fff 100%)';
                    this.style.borderColor = '#16CCC8';
                };
            });
            // منطق اختيار الجغرافيا وتوليد القوائم حسب الدولة
            var geoChoiceBtns = Array.from(document.querySelectorAll('.geo-choice-btn'));
            var geoActive = 'province';
            var geoListContainer = document.getElementById('geoListContainer');
            function renderGeoList() {
                var countryElem = document.getElementById('pricesSelectedCountryName');
                var country = countryElem && countryElem.textContent && countryElem.textContent.trim() !== '' ? countryElem.textContent.trim() : null;
                if (!country || !window.geoData || !window.geoData[country]) {
                    if (geoListContainer) geoListContainer.innerHTML = '<div style="color:#e74c3c;text-align:center;">يرجى اختيار الدولة</div>';
                    return;
                }
                var geoData = window.geoData[country];
                var html = '';
                if (geoActive === 'province') {
                    html = geoData.province.map(function(p) {
                        return `<span class="geo-item geo-province" data-name="${p.name}" style="display:inline-block;margin:6px 8px;padding:7px 18px;border-radius:8px;background:#f8f9fa;color:#444;cursor:pointer;border:1px solid #e0e0e0;">${p.name}</span>`;
                    }).join('');
                } else if (geoActive === 'district') {
                    var provinces = geoData.province;
                    var districts = [];
                    provinces.forEach(function(p){ districts = districts.concat(p.districts); });
                    html = districts.map(function(d) {
                        return `<span class="geo-item geo-district" data-name="${d.name}" style="display:inline-block;margin:6px 8px;padding:7px 18px;border-radius:8px;background:#f8f9fa;color:#444;cursor:pointer;border:1px solid #e0e0e0;">${d.name}</span>`;
                    }).join('');
                } else if (geoActive === 'subdistrict') {
                    var provinces = geoData.province;
                    var subdistricts = [];
                    provinces.forEach(function(p){
                        p.districts.forEach(function(d){
                            subdistricts = subdistricts.concat(d.subdistricts.map(function(s){ return {name:s, district:d.name}; }));
                        });
                    });
                    html = subdistricts.map(function(s) {
                        return `<span class="geo-item geo-subdistrict" data-name="${s.name}" style="display:inline-block;margin:6px 8px;padding:7px 18px;border-radius:8px;background:#f8f9fa;color:#444;cursor:pointer;border:1px solid #e0e0e0;">${s.name}</span>`;
                    }).join('');
                }
                if (geoListContainer) geoListContainer.innerHTML = html || '<div style="color:#e67e22;text-align:center;">لا توجد بيانات</div>';
            }
            geoChoiceBtns.forEach(function(btn) {
                btn.onclick = function() {
                    geoChoiceBtns.forEach(function(b){
                        b.style.color = '#888';
                    });
                    btn.style.color = '#16CCC8';
                    geoActive = btn.getAttribute('data-geo');
                    renderGeoList();
                };
            });
            // إعادة توليد القائمة عند تغيير الدولة
            var pricesCountryDropdown = document.getElementById('pricesCountryDropdown');
            if (pricesCountryDropdown) {
                pricesCountryDropdown.addEventListener('click', function(e){
                    setTimeout(renderGeoList, 100);
                });
            }
            renderGeoList();
            // لا تلوّن أي اختيار افتراضياً، جميعها رمادي حتى يضغط المستخدم
            var showProvincesBtn = document.getElementById('showProvincesBtn');
            var showAreasBtn = document.getElementById('showAreasBtn');
            var pricesListMode = 'provinces';
            if (showProvincesBtn && showAreasBtn) {
                showProvincesBtn.onclick = function() {
                    pricesListMode = 'provinces';
                    showProvincesBtn.style.background = 'linear-gradient(90deg, #227FCC, #16CCC8)';
                    showProvincesBtn.style.color = '#fff';
                    showAreasBtn.style.background = '#f8f9fa';
                    showAreasBtn.style.color = '#227FCC';
                };
                showAreasBtn.onclick = function() {
                    pricesListMode = 'areas';
                    showAreasBtn.style.background = 'linear-gradient(90deg, #227FCC, #16CCC8)';
                    showAreasBtn.style.color = '#fff';
                    showProvincesBtn.style.background = '#f8f9fa';
                    showProvincesBtn.style.color = '#227FCC';
                };
                // افتراضي
                showProvincesBtn.style.background = 'linear-gradient(90deg, #227FCC, #16CCC8)';
                showProvincesBtn.style.color = '#fff';
            }
            function updatePricesList() {
                var country = pricesSelectedCountryName.textContent;
                var type = null;
                pricesTypeRadios.forEach(function(radio) {
                    if (radio.style.background.includes('#16CCC8')) type = radio.getAttribute('data-type');
                });
                if (!country || country === 'اختر الدولة' || !type) {
                    return;
                }
                // بيانات المحافظات والمناطق حسب الدولة (محاكاة)
                var pricesData = {
                    'العراق': {
                        province: [
                            { name: 'بغداد', price: '180,000$', change: '+8%' },
                            { name: 'البصرة', price: '120,000$', change: '+12%' },
                            { name: 'أربيل', price: '90,000$', change: '+7%' },
                            { name: 'نينوى', price: '85,000$', change: '+4%' },
                            { name: 'النجف', price: '70,000$', change: '+3%' },
                            { name: 'كربلاء', price: '75,000$', change: '+5%' },
                            { name: 'ذي قار', price: '60,000$', change: '+2%' },
                            { name: 'صلاح الدين', price: '65,000$', change: '+2%' },
                            { name: 'ديالى', price: '68,000$', change: '+2%' },
                            { name: 'بابل', price: '72,000$', change: '+3%' }
                        ],
                        district: [
                            { name: 'قضاء الكرخ', price: '110,000$', change: '+4%' },
                            { name: 'قضاء الرصافة', price: '120,000$', change: '+5%' },
                            { name: 'قضاء الزبير', price: '90,000$', change: '+3%' }
                        ],
                        subdistrict: [
                            { name: 'ناحية الكرادة', price: '80,000$', change: '+2%' },
                            { name: 'ناحية الدورة', price: '85,000$', change: '+2%' }
                        ],
                        area: [
                            { name: 'حي الجامعة', price: '150,000$', change: '+7%' },
                            { name: 'حي الحسين', price: '120,000$', change: '+5%' }
                        ]
                    },
                    'مصر': {
                        province: [
                            { name: 'القاهرة', price: '95,000$', change: '+5%' },
                            { name: 'الإسكندرية', price: '80,000$', change: '+3%' },
                            { name: 'الجيزة', price: '70,000$', change: '+2%' }
                        ],
                        district: [
                            { name: 'قضاء مصر الجديدة', price: '100,000$', change: '+4%' },
                            { name: 'قضاء المعادي', price: '110,000$', change: '+5%' }
                        ],
                        subdistrict: [
                            { name: 'ناحية الزمالك', price: '120,000$', change: '+6%' },
                            { name: 'ناحية شبرا', price: '80,000$', change: '+3%' }
                        ],
                        area: [
                            { name: 'مدينة نصر', price: '110,000$', change: '+6%' },
                            { name: 'المعادي', price: '105,000$', change: '+5%' }
                        ]
                    }
                    // ... أضف بيانات لباقي الدول بنفس النمط ...
                };
                var dataObj = pricesData[country] || {province:[], district:[], subdistrict:[], area:[]};
                var list = dataObj[geoActive] || [];
                if (!list.length) {
                    return;
                }
                var html = list.map(function(p) {
                    return `<div style=\"display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;\">
                        <div>
                            <div style=\"font-weight: bold; color: #333; font-size: 13px;\">${p.name}</div>
                            <div style=\"color: #666; font-size: 11px;\">متوسط السعر</div>
                        </div>
                        <div style=\"text-align: left;\">
                            <div style=\"font-weight: bold; color: #16CCC8; font-size: 13px;\">${p.price}</div>
                            <div style=\"color: #27ae60; font-size: 11px;\"><i class=\"fas fa-arrow-up\"></i> ${p.change}</div>
                        </div>
                    </div>`;
                }).join('');
                // تم تعطيل عرض النتائج في pricesListContainer نهائياً
            }
            // تهيئة قائمة الدول
            var countryDropdown = document.getElementById('countryDropdown');
            var pricesCountryDropdown = document.getElementById('pricesCountryDropdown');
            var pricesCountryDropdown = document.getElementById('pricesCountryDropdown');
            if (countryDropdown) {
                countryDropdown.innerHTML = countries.map(function(c) {
                    return `<div class="country-option" data-flag="${c.flag}" data-name="${c.name}" style="padding: 8px 14px; display: flex; align-items: center; gap: 10px; cursor: pointer; font-size: 22px; transition: background 0.2s;">
                        <span style='font-size:22px;'>${c.flag}</span>
                        <span style='font-size:15px;'>${c.name}</span>
                    </div>`;
                }).join('');
            }
            if (pricesCountryDropdown) {
                pricesCountryDropdown.innerHTML = countries.map(function(c) {
                    return `<div class="country-option" data-flag="${c.flag}" data-name="${c.name}" style="padding: 8px 14px; display: flex; align-items: center; gap: 10px; cursor: pointer; font-size: 22px; transition: background 0.2s;">
                        <span style='font-size:22px;'>${c.flag}</span>
                        <span style='font-size:15px;'>${c.name}</span>
                    </div>`;
                }).join('');
            }
            // منطق فتح/إغلاق القائمة
            var countrySelectBox = document.getElementById('countrySelectBox');
            var pricesCountrySelectBox = document.getElementById('pricesCountrySelectBox');
            var selectedCountryName = document.getElementById('selectedCountryName');
            var selectedCountryFlag = document.getElementById('selectedCountryFlag');
            if (countrySelectBox && countryDropdown) {
                countrySelectBox.onclick = function(e) {
                    e.stopPropagation();
                    countryDropdown.style.display = countryDropdown.style.display === 'block' ? 'none' : 'block';
                };
                document.addEventListener('click', function() { countryDropdown.style.display = 'none'; });
                countryDropdown.onclick = function(e) {
                    var option = e.target.closest('.country-option');
                    if (option) {
                        var flag = option.getAttribute('data-flag');
                        var name = option.getAttribute('data-name');
                        // عرض العلم بجانب اسم الدولة المختارة في مستطيل التحليل (تحليل السوق)
                        if (selectedCountryName) {
                            selectedCountryName.innerHTML = `<span style='font-size:22px;vertical-align:middle;'>${flag}</span> <span style='font-size:15px;vertical-align:middle;'>${name}</span>`;
                        }
                        // عرض العلم بجانب اسم الدولة المختارة في مستطيل أسعار العقارات
                        var pricesSelectedCountryName = document.getElementById('pricesSelectedCountryName');
                        if (pricesSelectedCountryName) {
                            pricesSelectedCountryName.innerHTML = `<span style='font-size:22px;vertical-align:middle;'>${flag}</span> <span style='font-size:15px;vertical-align:middle;'>${name}</span>`;
                        }
                        countryDropdown.style.display = 'none';
                    }
                };
            }
            // دعم فتح قائمة الدول في أسعار العقارات
            if (pricesCountrySelectBox && pricesCountryDropdown) {
                pricesCountrySelectBox.onclick = function(e) {
                    e.stopPropagation();
                    pricesCountryDropdown.style.display = pricesCountryDropdown.style.display === 'block' ? 'none' : 'block';
                };
                document.addEventListener('click', function() { pricesCountryDropdown.style.display = 'none'; });
                pricesCountryDropdown.onclick = function(e) {
                    var option = e.target.closest('.country-option');
                    if (option) {
                        var flag = option.getAttribute('data-flag');
                        var name = option.getAttribute('data-name');
                        // عرض العلم بجانب اسم الدولة المختارة في مستطيل أسعار العقارات فقط
                        var pricesSelectedCountryName = document.getElementById('pricesSelectedCountryName');
                        if (pricesSelectedCountryName) {
                            pricesSelectedCountryName.innerHTML = `<span style='font-size:22px;vertical-align:middle;'>${flag}</span> <span style='font-size:15px;vertical-align:middle;'>${name}</span>`;
                        }
                        pricesCountryDropdown.style.display = 'none';
                    }
                };
            }
            // منطق اختيار نوع العقار
            var typeRadios = document.querySelectorAll('.type-radio');
            typeRadios.forEach(function(radio) {
                radio.onclick = function() {
                    typeRadios.forEach(function(r) {
                        r.style.background = '#fff';
                        r.style.borderColor = '#b0b0b0';
                    });
                    this.style.background = 'radial-gradient(circle at 60% 40%, #16CCC8 60%, #fff 100%)';
                    this.style.borderColor = '#16CCC8';
                };
            });
            // زر التحقق
            var pricesVerifyBtn = document.getElementById('pricesVerifyBtn');
            var pricesResultContainer = document.getElementById('pricesResultContainer');
            // ملاحظة: إذا أردت كل محافظات العراق أضفها في geoData.js بنفس النمط
            // زيادة المسافة تحت زر التحقق
            if (pricesResultContainer) {
                pricesResultContainer.style.marginTop = '38px';
            }
            if (pricesVerifyBtn) {
                pricesVerifyBtn.onclick = function() {
                    // جلب القيم المختارة
                    var countryElem = document.getElementById('pricesSelectedCountryName');
                    var country = countryElem && countryElem.textContent && countryElem.textContent.trim() !== '' ? countryElem.textContent.trim() : null;
                    if (country === null || country === '' || country === 'اختر الدولة') country = null;
                    var type = null;
                    var typeRadios = document.querySelectorAll('.prices-type-radio');
                    typeRadios.forEach(function(radio) {
                        if (radio.style.background.includes('#16CCC8')) type = radio.getAttribute('data-type');
                    });
                    var geoType = null;
                    var geoBtns = document.querySelectorAll('.geo-choice-btn');
                    geoBtns.forEach(function(btn) {
                        if (btn.style.color === 'rgb(22, 204, 200)' || btn.style.color === '#16CCC8') geoType = btn.getAttribute('data-geo');
                    });
                    // تحقق من اختيار الدولة والنوع
                    if (!country || !type) {
                        if (pricesResultContainer) {
                            pricesResultContainer.innerHTML = '<div style="color:#e74c3c;font-size:17px;text-align:center;">يرجى اختيار الدولة والنوع أولاً</div>';
                        }
                        return;
                    }
                    // تحقق من اختيار نوع الجغرافيا
                    if (!geoType) {
                        if (pricesResultContainer) {
                            pricesResultContainer.innerHTML = '<div style="color:#e67e22;font-size:16px;text-align:center;">يرجى اختيار نوع الجغرافيا (محافظة/قضاء/ناحية/منطقة)</div>';
                        }
                        return;
                    }
                    // جلب البيانات وعرض القائمة
                    var geoData = window.geoData && window.geoData[country] && window.geoData[country][geoType];
                    if (!geoData || !Array.isArray(geoData) || geoData.length === 0) {
                        pricesResultContainer.innerHTML = '<div style="color:#e67e22;font-size:16px;text-align:center;">لا توجد بيانات متاحة</div>';
                        return;
                    }
                    var html = `<div style='background:#f8f9fa;border-radius:12px;padding:18px 8px;text-align:center;font-size:17px;color:#227FCC;'>
                        <b>الدولة:</b> ${country} <br>
                        <b>النوع:</b> ${type} <br>
                        <b>${geoType === 'province' ? 'المحافظة' : geoType === 'district' ? 'القضاء' : geoType === 'subdistrict' ? 'الناحية' : 'المنطقة'}</b>
                        <div style='margin-top:12px;'>
                            <table style='width:100%;border-collapse:collapse;'>
                                <tr style='background:#e4e6ea;color:#227FCC;font-size:15px;'>
                                    <th style='padding:6px 0;'>الاسم</th>
                                    <th style='padding:6px 0;'>السعر (${type})</th>
                                </tr>
                                ${geoData.map(function(item){
                                    return `<tr style='border-bottom:1px solid #e4e6ea;'>
                                        <td style='padding:6px 0;'>${item.name}</td>
                                        <td style='padding:6px 0;'>${item.prices && item.prices[type] ? item.prices[type] : '-'}</td>
                                    </tr>`;
                                }).join('')}
                            </table>
                        </div>
                    </div>`;
                    pricesResultContainer.innerHTML = html;
                };
            }

        // تحميل الرسم البياني عند تحميل الصفحة

        // إنشاء قصص البائعين العقاريين فقط
        function generateSellersStories(serviceName) {
            const stories = [];
            const icons = [
                'fas fa-home', 'fas fa-building', 'fas fa-city', 'fas fa-home', 'fas fa-map-marked-alt',
                'fas fa-hammer', 'fas fa-building', 'fas fa-key', 'fas fa-crown', 'fas fa-chess-rook',
                'fas fa-chart-line', 'fas fa-home', 'fas fa-store', 'fas fa-tools', 'fas fa-mosque',
                'fas fa-seedling', 'fas fa-building', 'fas fa-users', 'fas fa-globe', 'fas fa-water',
                'fas fa-home', 'fas fa-building', 'fas fa-city', 'fas fa-home', 'fas fa-map-marked-alt',
                'fas fa-hammer'
            ];
            const sellerNames = [
                "أحمد العاني", "سارة الجبوري", "محمد السعدي", "ليلى الكعبي", "علي الموسوي", "نور الزبيدي", "حسن التميمي", "زينب الراوي", "يوسف الدليمي", "مريم الشمري", "عمر الفهداوي", "هدى عبد الله", "سيف الجنابي", "آية الساعدي", "كرار الحسيني", "شهد العطار", "حيدر عبد الكريم", "بتول عبد الرضا", "مصطفى عبد الله", "إيمان عبد الجبار", "حسين كاظم", "سندس عبد الأمير", "عمار عبد الحسين", "شيماء عبد الله", "حسنين عبد الرضا", "زينب عبد الكريم"
            ];
            for (let i = 0; i < 26; i++) {
                const letter = String.fromCharCode(65 + i); // 'A' to 'Z'
                stories.push({
                    name: sellerNames[i] || `بائع ${letter}`,
                    avatar: '../Eqarat/Stories/' + letter + '.jpg',
                    icon: icons[i % icons.length]
                });
            }
            return stories;
        }

        // إنشاء البث المباشر العقاري
        function generateLiveStreams(serviceName) {
            const streams = [];

            // بث مباشر للعقارات (15 بث)
            streams.push(
                { name: 'عقارات الذهبية', avatar: '../Eqarat/Live/1.jpg', icon: 'fas fa-home', isLive: true },
                { name: 'مكتب الإسكان', avatar: '../Eqarat/Live/2.jpg', icon: 'fas fa-building', isLive: false },
                { name: 'بغداد الحديثة', avatar: '../Eqarat/Live/3.jpg', icon: 'fas fa-city', isLive: true },
                { name: 'عقارات الكرادة', avatar: '../Eqarat/Live/4.jpg', icon: 'fas fa-home', isLive: true },
                { name: 'الأراضي الاستثمارية', avatar: '../Eqarat/Live/5.jpg', icon: 'fas fa-map-marked-alt', isLive: false },
                { name: 'البناء الحديث', avatar: '../Eqarat/Live/6.jpg', icon: 'fas fa-hammer', isLive: true },
                { name: 'عقارات الجادرية', avatar: '../Eqarat/Live/7.jpg', icon: 'fas fa-building', isLive: false },
                { name: 'الشقق الفاخرة', avatar: '../Eqarat/Live/8.jpg', icon: 'fas fa-key', isLive: true },
                { name: 'المنصور الراقية', avatar: '../Eqarat/Live/9.jpg', icon: 'fas fa-crown', isLive: false },
                { name: 'الفلل الفاخرة', avatar: '../Eqarat/Live/10.jpg', icon: 'fas fa-chess-rook', isLive: true },
                { name: 'الاستثمار العقاري', avatar: '../Eqarat/Live/11.jpg', icon: 'fas fa-chart-line', isLive: true },
                { name: 'عقارات الزعفرانية', avatar: '../Eqarat/Live/12.jpg', icon: 'fas fa-home', isLive: false },
                { name: 'العقارات التجارية', avatar: '../Eqarat/Live/13.jpg', icon: 'fas fa-store', isLive: true },
                { name: 'التطوير العقاري', avatar: '../Eqarat/Live/14.jpg', icon: 'fas fa-tools', isLive: false },
                { name: 'عقارات الكاظمية', avatar: '../Eqarat/Live/15.jpg', icon: 'fas fa-mosque', isLive: true }
            );

            return streams;
        }

        // إنشاء العروض المميزة العقارية
        function generatePremiumOffers(serviceName) {
            // صور العروض من مجلد Special-offers (EA.jpg إلى ET.jpg)
            const imageNames = ['EA', 'EB', 'EC', 'ED', 'EE', 'EF', 'EG', 'EH', 'EI', 'EJ', 'EK', 'EL', 'EM', 'EN', 'EO', 'EP', 'EQ', 'ER', 'ES', 'ET'];
            const offers = [];
            for (let i = 0; i < imageNames.length; i++) {
                offers.push({
                    title: `عرض مميز ${i + 1}`,
                    description: `تفاصيل العرض المميز رقم ${i + 1}`,
                    price: `${(i + 1) * 10000}$`,
                    seller: `بائع ${i + 1}`,
                    icon: 'fas fa-star',
                    image: '../Eqarat/Special-offers/' + imageNames[i] + '.jpg'
                });
            }
            return offers;
        }

        // إنشاء منشورات البائعين من دول عربية مختلفة
        function generateSellersPosts(serviceName) {
            const posts = [];
            for (let i = 1; i <= 34; i++) {
                posts.push({
                    id: i,
                    author: `بائع ${i}`,
                    time: `منذ ${i} ساعة`,
                    country: 'العراق',
                    countryFlag: '🇮🇶',
                    countryName: 'العراق',
                    countryColor: '#007A3D',
                    content: `فرصة عقارية رائعة للبيع أو الإيجار. لمزيد من التفاصيل تواصل معنا مباشرة!`,
                    images: [`../Eqarat/Publications/F${i}.jpg`],
                    avatar: `../Eqarat/Publications/F${i}.jpg`,
                    likes: 100 + i * 3,
                    comments: 20 + i,
                    shares: 5 + i
                });
            }
            return posts;
        }

        // دوال التمرير للقصص
        function scrollStories(direction) {
            const container = document.getElementById('sellersStoriesContainer');
            const scrollAmount = 400; // زيادة مسافة التمرير لتناسب العدد الكبير

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updateArrowsState();
            }, 400);
        }

        // تحديث حالة الأسهم
        function updateArrowsState() {
            const container = document.getElementById('sellersStoriesContainer');
            const leftArrow = document.getElementById('storiesLeftArrow');
            const rightArrow = document.getElementById('storiesRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دوال التمرير للبث المباشر
        function scrollLiveStreams(direction) {
            const container = document.getElementById('liveStreamsContainer');
            const scrollAmount = 400;

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updateLiveArrowsState();
            }, 400);
        }

        // تحديث حالة أسهم البث المباشر
        function updateLiveArrowsState() {
            const container = document.getElementById('liveStreamsContainer');
            const leftArrow = document.getElementById('liveLeftArrow');
            const rightArrow = document.getElementById('liveRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دالة بدء بث مباشر
        function startLiveStream() {
            alert('🔴 بدء بث مباشر!\n\nسيتم فتح نافذة البث المباشر...\n\n📹 يمكنك بث فيديو مباشر\n💬 التفاعل مع المشاهدين\n📊 مراقبة الإحصائيات');
        }

        // دوال التمرير للعروض المميزة
        function scrollPremiumOffers(direction) {
            const container = document.getElementById('premiumOffersContainer');
            const scrollAmount = 400;

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updatePremiumArrowsState();
            }, 400);
        }

        // تحديث حالة أسهم العروض المميزة
        function updatePremiumArrowsState() {
            const container = document.getElementById('premiumOffersContainer');
            const leftArrow = document.getElementById('premiumLeftArrow');
            const rightArrow = document.getElementById('premiumRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دالة فتح العرض المميز
        function openServicePremiumOffer(title) {
            alert('⭐ عرض مميز!\n\n' + title + '\n\nسيتم فتح تفاصيل العرض...\n\n💰 معلومات السعر\n📞 تفاصيل التواصل\n📍 الموقع على الخريطة');
        }

        // تحميل منشورات البائعين (تصميم Profile.html)
        function loadSellersPosts(serviceName) {
            const container = document.getElementById('sellersPostsContainer');
            const posts = generateSellersPosts(serviceName);

            // أسماء للبائعين (عددها 34)
            const sellerNames = [
                "أحمد العاني", "سارة الجبوري", "محمد السعدي", "ليلى الكعبي", "علي الموسوي", "نور الزبيدي", "حسن التميمي", "زينب الراوي", "يوسف الدليمي", "مريم الشمري", "عمر الفهداوي", "هدى عبد الله", "سيف الجنابي", "آية الساعدي", "كرار الحسيني", "شهد العطار", "حيدر عبد الكريم", "بتول عبد الرضا", "مصطفى عبد الله", "إيمان عبد الجبار", "حسين كاظم", "سندس عبد الأمير", "عمار عبد الحسين", "شيماء عبد الله", "حسنين عبد الرضا", "زينب عبد الكريم", "محمد عبد الجبار", "سارة عبد الحسين", "علي عبد الله", "نور عبد الرضا", "حسن عبد الجبار", "ليلى عبد الحسين", "يوسف عبد الكريم", "مريم عبد الله"
            ];
            // أعلام دول متنوعة (عربية وأجنبية)
            const flags = [
                'iq', 'eg', 'sa', 'ae', 'jo', 'ma', 'dz', 'tn', 'sy', 'lb', 'tr', 'us', 'fr', 'de', 'gb', 'it', 'es', 'ru', 'cn', 'in', 'br', 'ca', 'se', 'ch', 'nl', 'kr', 'jp', 'uae', 'kw', 'qa', 'om', 'bh', 'sd', 'ps', 'ye'
            ];
            container.innerHTML = posts.map((post, i) => {
                // توزيع عشوائي بين متوفر ومباع
                const isSold = i % 4 === 0; // كل رابع منشور مباع
                const statusText = isSold ? 'مباع' : 'متوفر';
                const statusClass = isSold ? 'seller-status-label seller-status-sold' : 'seller-status-label seller-status-available';
                return `
                <div class="seller-post-card" id="post-card-${post.id}" style="position: relative;">
                    <!-- شريط الأدوات أعلى يمين المنشور (منسق) -->
                    <div style="position: absolute; top: 14px; right: 16px; display: flex; gap: 18px; z-index: 10; align-items: center;">
                        <!-- زر إغلاق -->
                        <span title="إغلاق" style="cursor: pointer; color: #888; font-size: 20px; display: flex; align-items: center; transition: color 0.2s;" onclick="closePost(${post.id})">
                            <i class="fas fa-times"></i>
                        </span>
                        <!-- زر تكبير/تصغير -->
                        <span title="تكبير/تصغير" style="cursor: pointer; color: #888; font-size: 20px; display: flex; align-items: center; transition: color 0.2s;" onclick="togglePostFullscreen(${post.id})">
                            <i id="fullscreen-icon-${post.id}" class="fas fa-expand"></i>
                        </span>
                        <!-- زر النقاط الثلاث -->
                        <span title="خيارات الخصوصية" style="cursor: pointer; color: #888; font-size: 20px; display: flex; align-items: center; position: relative; transition: color 0.2s;" onclick="togglePrivacyMenu(event, ${post.id})">
                            <i class="fas fa-ellipsis-v"></i>
                            <div id="privacy-menu-${post.id}" class="privacy-menu" style="display: none; position: absolute; top: 120%; right: 0; background: #fff; border: 1px solid #ccc; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); z-index: 100; min-width: 270px; width: 270px;">
                                ${['العامة','المتابعون','متابعين المتابعون','انا فقط'].map((label, idx) => {
                                    const icons = ['fa-globe','fa-user-friends','fa-users','fa-lock'];
                                    return `
                                    <div onclick="setPostPrivacy(${post.id}, '${label}')" style="padding: 10px 18px; cursor: pointer; color: #888; font-size: 15px; border-bottom: ${idx<3?'1px solid #eee':'none'}; display: flex; align-items: center; gap: 10px; transition: color 0.2s; position: relative;" onmouseover="this.style.color='#16CCC8'; this.querySelector('i').style.color='#16CCC8';" onmouseout="this.style.color='#888'; this.querySelector('i').style.color='#888';">
                                        <i class='fas ${icons[idx]}' style='color: #888; font-size: 16px; transition: color 0.2s;'></i> ${label}
                                        <span class="privacy-switch" id="privacy-switch-${post.id}-${idx}" style="margin-right:auto; display: flex; align-items: center;">
                                            <span style="width: 38px; height: 22px; background: #eee; border-radius: 12px; display: flex; align-items: center; position: relative; transition: background 0.2s;">
                                                <span id="privacy-switch-dot-${post.id}-${idx}" style="width: 16px; height: 16px; background: #888; border-radius: 50%; position: absolute; right: 3px; transition: right 0.2s, background 0.2s;"></span>
                                            </span>
                                        </span>
                                    </div>
                                    `;
                                }).join('')}
                            </div>
                        </span>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 6px; margin-bottom: 8px;">
                        <!-- صورة العلم -->
                        <img class="seller-flag-img" src="https://flagcdn.com/w40/${flags[i%flags.length]}.png" 
                             alt="علم الدولة" style="margin: 0;">

                        <!-- السهم + النص بجانبه -->
                        <div style="display: flex; align-items: center; gap: 6px; position: relative;">
                            <span style="cursor: pointer; display: flex; align-items: center;" onclick="toggleStatusDropdown(event, ${post.id})">
                                <i id="status-arrow-${post.id}" class="fas fa-chevron-down" style="font-size: 14px; color: #888;"></i>
                            </span>
                            <span id="status-text-${post.id}" style="font-size: 14px; font-weight: 500; color: ${statusText === 'متوفر' ? '#227FCC' : '#e74c3c'};">
                                ${statusText}
                            </span>
                            <!-- القائمة المنسدلة -->
                            <div id="status-dropdown-${post.id}" class="status-dropdown" 
                                 style="display: none; position: absolute; top: 120%; left: 0; background: #fff; 
                                        border: 1px solid #ccc; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); 
                                        z-index: 100; min-width: 100px;">
                                <div onclick="setPostStatus(${post.id}, 'متوفر')" 
                                     style="padding: 8px 12px; cursor: pointer; color: #227FCC; font-size: 14px; border-bottom: 1px solid #eee;">
                                    متوفر
                                </div>
                                <div onclick="setPostStatus(${post.id}, 'غير متوفر')" 
                                     style="padding: 8px 12px; cursor: pointer; color: #e74c3c; font-size: 14px;">
                                    غير متوفر
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="seller-header-row">
                        <img class="seller-avatar" src="${post.avatar}" alt="${sellerNames[i] || post.author}">
                        <div class="seller-info">
                            <div class="seller-name">${sellerNames[i] || post.author}</div>
                            <div class="seller-time">${post.time}</div>
                        </div>
                    </div>
                    ${post.images && post.images.length > 0 ? `
                        <img class="seller-post-image" src="${post.images[0]}" alt="صورة المنشور" onclick="openImageModal('${post.images[0]}')">
                    ` : ''}
                    <div class="seller-post-content">${post.content}</div>
                    <div class="seller-post-stats">
                        <div class="seller-post-stat">
                            <span class="seller-post-stat-number">${post.likes}</span>
                            <i class="fas fa-heart seller-post-stat-icon"></i>
                        </div>
                        <div class="seller-post-stat">
                            <span class="seller-post-stat-number">${post.comments}</span>
                            <i class="fas fa-comment seller-post-stat-icon"></i>
                        </div>
                        <div class="seller-post-stat">
                            <span class="seller-post-stat-number">${post.shares}</span>
                            <i class="fas fa-share seller-post-stat-icon"></i>
                        </div>
                    </div>
                    <div class="post-actions">
                        <div class="post-action" onclick="likePost(${post.id})">
                            <i class="fas fa-heart"></i>
                            <span>إعجاب</span>
                        </div>
                        <div class="post-action" onclick="commentPost(${post.id})">
                            <i class="fas fa-comment"></i>
                            <span>تعليق</span>
                        </div>
                        <div class="post-action" onclick="sharePost(${post.id})">
                            <i class="fas fa-share"></i>
                            <span>مشاركة</span>
                        </div>
                    </div>
                </div>
                `;
            }).join('');
        }
        // دالة إظهار/إخفاء القائمة المنسدلة للحالة
        // دالة إغلاق المنشور
        window.closePost = function(postId) {
            var card = document.getElementById('post-card-' + postId);
            if (card) card.style.display = 'none';
        }

        // دالة تكبير/تصغير المنشور
        window.togglePostFullscreen = function(postId) {
            var card = document.getElementById('post-card-' + postId);
            var icon = document.getElementById('fullscreen-icon-' + postId);
            if (!card) return;
            if (!card.classList.contains('fullscreen')) {
                card.classList.add('fullscreen');
                card.style.position = 'fixed';
                card.style.top = '50%';
                card.style.left = '50%';
                card.style.transform = 'translate(-50%, -50%)';
                card.style.zIndex = '9999';
                card.style.width = '700px';
                card.style.height = 'auto';
                card.style.maxWidth = '95vw';
                card.style.maxHeight = '90vh';
                card.style.overflowY = 'auto';
                card.style.background = '#fff';
                card.style.borderRadius = '18px';
                card.style.boxShadow = '0 8px 40px rgba(0,0,0,0.18)';
                // تنسيق الصورة داخل المنشور المكبر
                var img = card.querySelector('.seller-post-image');
                if (img) {
                    img.style.width = '100%';
                    img.style.height = 'auto';
                    img.style.maxHeight = '350px';
                    img.style.objectFit = 'cover';
                    img.style.borderRadius = '12px';
                    img.style.marginBottom = '18px';
                }
                // تنسيق النص
                var content = card.querySelector('.seller-post-content');
                if (content) {
                    content.style.fontSize = '20px';
                    content.style.lineHeight = '1.7';
                    content.style.textAlign = 'center';
                    content.style.margin = '18px 0';
                }
                if (icon) { icon.classList.remove('fa-expand'); icon.classList.add('fa-compress'); }
            } else {
                card.classList.remove('fullscreen');
                card.style.position = '';
                card.style.top = '';
                card.style.left = '';
                card.style.transform = '';
                card.style.zIndex = '';
                card.style.width = '';
                card.style.height = '';
                card.style.maxWidth = '';
                card.style.maxHeight = '';
                card.style.overflowY = '';
                card.style.background = '';
                card.style.borderRadius = '';
                card.style.boxShadow = '';
                // إعادة الصورة للوضع الطبيعي
                var img = card.querySelector('.seller-post-image');
                if (img) {
                    img.style.width = '';
                    img.style.height = '';
                    img.style.maxHeight = '';
                    img.style.objectFit = '';
                    img.style.borderRadius = '';
                    img.style.marginBottom = '';
                }
                // إعادة النص للوضع الطبيعي
                var content = card.querySelector('.seller-post-content');
                if (content) {
                    content.style.fontSize = '';
                    content.style.lineHeight = '';
                    content.style.textAlign = '';
                    content.style.margin = '';
                }
                if (icon) { icon.classList.remove('fa-compress'); icon.classList.add('fa-expand'); }
            }
        }

        // دالة إظهار/إخفاء قائمة الخصوصية
        window.togglePrivacyMenu = function(event, postId) {
            event.stopPropagation();
            // إغلاق أي قائمة خصوصية مفتوحة
            document.querySelectorAll('.privacy-menu').forEach(el => el.style.display = 'none');
            var menu = document.getElementById('privacy-menu-' + postId);
            if (menu) menu.style.display = 'block';
        }

        // دالة تعيين الخصوصية
        window.setPostPrivacy = function(postId, privacy) {
            var menu = document.getElementById('privacy-menu-' + postId);
            if (menu) menu.style.display = 'none';
            // تحديث السويتشات
            var options = ['العامة','المتابعون','متابعين المتابعون','انا فقط'];
            options.forEach(function(opt, idx) {
                var dot = document.getElementById('privacy-switch-dot-' + postId + '-' + idx);
                var bar = dot && dot.parentElement;
                if (dot && bar) {
                    if (opt === privacy) {
                        dot.style.right = '19px';
                        dot.style.background = '#16CCC8';
                        bar.style.background = '#d0f6f5';
                    } else {
                        dot.style.right = '3px';
                        dot.style.background = '#888';
                        bar.style.background = '#eee';
                    }
                }
            });
            // يمكن هنا إضافة منطق حفظ الخصوصية إذا رغبت
        }

        // إغلاق قائمة الخصوصية عند الضغط خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.privacy-menu') && !e.target.closest('.fa-ellipsis-v')) {
                document.querySelectorAll('.privacy-menu').forEach(el => el.style.display = 'none');
            }
        });
        function toggleStatusDropdown(event, postId) {
            event.stopPropagation();
            // إغلاق أي قائمة مفتوحة أخرى
            document.querySelectorAll('.status-dropdown').forEach(el => el.style.display = 'none');
            const dropdown = document.getElementById('status-dropdown-' + postId);
            if (dropdown) {
                dropdown.style.display = 'block';
            }
        }

        // تصدير الدوال للنطاق العام حتى تعمل مع onclick
    window.toggleStatusDropdown = toggleStatusDropdown;
    window.setPostStatus = setPostStatus;


        // دالة تغيير حالة المنشور
        function setPostStatus(postId, status) {
            const statusText = document.getElementById('status-text-' + postId);
            const statusArrow = document.getElementById('status-arrow-' + postId);
            if (statusText) {
                statusText.textContent = status;
                if (status === 'متوفر') {
                    statusText.style.color = '#227FCC';
                } else {
                    statusText.style.color = '#e74c3c';
                }
                if (statusArrow) statusArrow.style.color = '#888';
            }
            // إغلاق القائمة
            const dropdown = document.getElementById('status-dropdown-' + postId);
            if (dropdown) dropdown.style.display = 'none';
    }

        // إغلاق القائمة عند الضغط خارجها
        document.addEventListener('click', function(e) {
            // إذا كان الضغط خارج أي عنصر يحمل .status-dropdown أو السهم
            if (!e.target.closest('.status-dropdown') && !e.target.closest('.fa-chevron-down')) {
                document.querySelectorAll('.status-dropdown').forEach(el => el.style.display = 'none');
            }
        });

        // تحميل العروض المميزة
        function loadPremiumOffers(serviceName) {
            const container = document.getElementById('premiumOffersContainer');
            const offers = generatePremiumOffers(serviceName);

            container.innerHTML = offers.map(offer => `
                <div class="premium-offer-card" onclick="openServicePremiumOffer('${offer.title}')" style="
                    width: 380px;
                    height: 200px;
                    border-radius: 15px;
                    border: 3px solid #FFD700;
                    background: linear-gradient(135deg, #FFF8DC, #FFFACD);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    cursor: pointer;
                    transition: all 0.3s;
                    position: relative;
                    flex-shrink: 0;
                    overflow: hidden;
                ">
                    <div style="
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
                        animation: shimmer 3s infinite;
                    "></div>

                    <div style="
                        width: 100%;
                        height: 100%;
                        position: relative;
                        z-index: 2;
                        overflow: hidden;
                        border-radius: 12px;
                    ">
                        <img src="${offer.image}" alt="${offer.title}" style="
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            border-radius: 12px;
                        " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="
                            display: none;
                            width: 100%;
                            height: 100%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 48px;
                            border-radius: 12px;
                        ">
                            <i class="${offer.icon}"></i>
                        </div>

                        <div style="
                            position: absolute;
                            top: 0;
                            right: 0;
                            background: linear-gradient(135deg, rgba(0,0,0,0.5), rgba(0,0,0,0.3));
                            color: white;
                            padding: 15px;
                            z-index: 3;
                            border-radius: 0 12px 0 15px;
                            max-width: 70%;
                        ">
                            <div style="
                                font-size: 14px;
                                font-weight: bold;
                                margin-bottom: 3px;
                                text-shadow: 2px 2px 4px rgba(0,0,0,0.9);
                                line-height: 1.2;
                            ">${offer.title}</div>
                            <div style="
                                font-size: 11px;
                                opacity: 0.95;
                                text-shadow: 1px 1px 3px rgba(0,0,0,0.9);
                                line-height: 1.3;
                            ">${offer.description}</div>
                        </div>
                    </div>

                    <div style="
                        position: absolute;
                        bottom: 5px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 200px;
                        height: 35px;
                        z-index: 10;
                    ">
                        <div style="
                            background: linear-gradient(135deg, #FFD700, #FFA500);
                            color: #8B4513;
                            padding: 8px 16px;
                            border-radius: 20px;
                            font-weight: bold;
                            font-size: 12px;
                            text-align: center;
                            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
                            border: 2px solid #FFD700;
                        ">عرض مميز - ${offer.price}</div>
                    </div>
                </div>
            `).join('');
        }

        // دالة إنشاء قصة جديدة
        function createNewStory() {
            alert('🎬 إنشاء قصة جديدة!\n\nسيتم فتح نافذة إنشاء القصة...\n\n📸 يمكنك إضافة صور وفيديوهات\n📝 كتابة وصف للقصة\n🏷️ إضافة علامات تجارية');
        }

        // دوال التفاعل مع المنشورات
        function likePost(postId) {
            alert('❤️ تم الإعجاب بالمنشور رقم ' + postId);
        }

        function commentPost(postId) {
            alert('💬 إضافة تعليق على المنشور رقم ' + postId);
        }

        function sharePost(postId) {
            alert('📤 مشاركة المنشور رقم ' + postId);
        }

        function openImageModal(imageSrc) {
            alert('🖼️ عرض الصورة بحجم كامل:\n' + imageSrc);
        }

        // دالة تغيير نوع الرسم البياني
        function changeChartType(type) {
            // إزالة التحديد من جميع الأزرار
            document.getElementById('weeklyBtn').style.background = 'transparent';
            document.getElementById('weeklyBtn').style.color = '#666';
            document.getElementById('monthlyBtn').style.background = 'transparent';
            document.getElementById('monthlyBtn').style.color = '#666';
            document.getElementById('yearlyBtn').style.background = 'transparent';
            document.getElementById('yearlyBtn').style.color = '#666';

            // تحديد الزر المختار
            const selectedBtn = document.getElementById(type + 'Btn');
            selectedBtn.style.background = 'linear-gradient(135deg, #16CCC8, #227FCC)';
            selectedBtn.style.color = 'white';

            // تحديث العنوان
            const chartTitle = document.getElementById('chartTitle');
            if (type === 'weekly') {
                chartTitle.textContent = '📊 الأداء الأسبوعي';
            } else if (type === 'monthly') {
                chartTitle.textContent = '📊 الأداء الشهري';
            } else {
                chartTitle.textContent = '📊 الأداء السنوي';
            }

            console.log('تم تغيير نوع الرسم البياني إلى:', type);
        }

        // تحميل البث المباشر
        function loadLiveStreams(serviceName) {
            const container = document.getElementById('liveStreamsContainer');
            const streams = generateLiveStreams(serviceName);

            container.innerHTML = streams.map((stream, i) => `
                <div style="
                    min-width: 75px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                    <div style="
                        width: 65px;
                        height: 65px;
                        border-radius: 50%;
                        background: linear-gradient(45deg, #8e44ad, #e74c3c);
                        padding: 3px;
                        margin: 0 auto 8px;
                        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        overflow: hidden;
                        position: relative;
                    ">
                        <img src="${stream.avatar}" alt="${stream.name}" style="width: 59px; height: 59px; border-radius: 50%; object-fit: cover;">
                        ${stream.isLive ? `<div style="position: absolute; bottom: 2px; right: 2px; background: #e74c3c; color: white; border-radius: 50%; width: 18px; height: 18px; display: flex; align-items: center; justify-content: center; font-size: 8px; font-weight: bold; border: 2px solid white; animation: pulse 1.5s infinite;">LIVE</div>` : ''}
                    </div>
                    <div style="font-size: 11px; color: #333; font-weight: 600; line-height: 1.2; max-width: 75px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${stream.name.length > 8 ? stream.name.substring(0, 8) + '...' : stream.name}</div>
                </div>
            `).join('');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل قصص البائعين للعقارات
            loadSellersStories('عقارات');

            // تحميل البث المباشر للعقارات
            loadLiveStreams('عقارات');

            // تحميل العروض المميزة للعقارات
            loadPremiumOffers('عقارات');

            // تحميل منشورات البائعين للعقارات
            loadSellersPosts('عقارات');

            // تحديث حالة الأسهم عند التحميل
            setTimeout(() => {
                updateArrowsState();
                updateLiveArrowsState();
                updatePremiumArrowsState();
            }, 100);

            // تحديث الأسهم عند التمرير اليدوي
            const storiesContainer = document.getElementById('sellersStoriesContainer');
            const liveContainer = document.getElementById('liveStreamsContainer');
            const premiumContainer = document.getElementById('premiumOffersContainer');

            storiesContainer.addEventListener('scroll', updateArrowsState);
            liveContainer.addEventListener('scroll', updateLiveArrowsState);
            premiumContainer.addEventListener('scroll', updatePremiumArrowsState);

            console.log('تم تحميل شريط القصص بنجاح! 🎉');
            console.log('تم تحميل شريط البث المباشر بنجاح! 🔴');
            console.log('تم تحميل العروض المميزة بنجاح! ⭐');
            console.log('تم تحميل منشورات البائعين بنجاح! 📰');
            console.log('عدد القصص المحملة:', generateSellersStories('عقارات').length);
            console.log('عدد البث المباشر:', generateLiveStreams('عقارات').length);
            console.log('عدد العروض المميزة:', generatePremiumOffers('عقارات').length);
            console.log('عدد المنشورات:', generateSellersPosts('عقارات').length);
        });
    </script>
// ...existing code...
</script>
<script>
// نافذة تفاصيل المشاريع الحكومية
window.showProjectDetails = function(title, desc, price) {
    document.getElementById('govProjectModalTitle').textContent = title;
    document.getElementById('govProjectModalDesc').textContent = desc;
    document.getElementById('govProjectModalPrice').textContent = price;
    document.getElementById('govProjectModal').style.display = 'flex';
}
window.closeGovProjectModal = function() {
    document.getElementById('govProjectModal').style.display = 'none';
}

// بيانات الدول مع الأعلام - نفس طريقة منشورات البائعين
const countriesData = {
    'العراق': { flag: '🇮🇶', name: 'العراق' },
    'مصر': { flag: '🇪🇬', name: 'مصر' },
    'السعودية': { flag: '🇸🇦', name: 'السعودية' },
    'الإمارات': { flag: '🇦🇪', name: 'الإمارات' },
    'سوريا': { flag: '🇸🇾', name: 'سوريا' },
    'لبنان': { flag: '🇱🇧', name: 'لبنان' },
    'الأردن': { flag: '🇯🇴', name: 'الأردن' },
    'تونس': { flag: '🇹🇳', name: 'تونس' },
    'المغرب': { flag: '🇲🇦', name: 'المغرب' },
    'الكويت': { flag: '🇰🇼', name: 'الكويت' },
    'قطر': { flag: '🇶🇦', name: 'قطر' },
    'البحرين': { flag: '🇧🇭', name: 'البحرين' },
    'فرنسا': { flag: '🇫🇷', name: 'فرنسا' },
    'ألمانيا': { flag: '🇩🇪', name: 'ألمانيا' },
    'إيطاليا': { flag: '🇮🇹', name: 'إيطاليا' },
    'إسبانيا': { flag: '🇪🇸', name: 'إسبانيا' },
    'بريطانيا': { flag: '🇬🇧', name: 'بريطانيا' },
    'تركيا': { flag: '🇹🇷', name: 'تركيا' }
};

// تبديل عرض قائمة الدول
function toggleCountryDropdown() {
    const dropdown = document.getElementById('countryOptions');
    const arrow = document.getElementById('dropdownArrow');

    if (dropdown.style.display === 'none' || dropdown.style.display === '') {
        dropdown.style.display = 'block';
        arrow.style.transform = 'rotate(180deg)';
        loadCountryOptions();
    } else {
        dropdown.style.display = 'none';
        arrow.style.transform = 'rotate(0deg)';
    }
}

// تحميل خيارات الدول
function loadCountryOptions() {
    const container = document.getElementById('countryOptions');
    container.innerHTML = Object.keys(countriesData).map((countryKey, index) => {
        const country = countriesData[countryKey];
        const isLast = index === Object.keys(countriesData).length - 1;
        return `
            <div onclick="selectCountry('${countryKey}')" style="
                padding: 14px 18px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 15px;
                transition: all 0.3s ease;
                ${!isLast ? 'border-bottom: 1px solid #f0f0f0;' : ''}
                font-family: 'Cairo', sans-serif;
            " onmouseover="
                this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)';
                this.style.transform='translateX(3px)';
            " onmouseout="
                this.style.background='white';
                this.style.transform='translateX(0)';
            ">
                <span style="
                    font-size: 20px;
                    width: 30px;
                    text-align: center;
                    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
                ">${country.flag}</span>
                <span style="
                    font-weight: 600;
                    color: #333;
                    font-size: 15px;
                ">${country.name}</span>
            </div>
        `;
    }).join('');
}

// اختيار دولة
function selectCountry(countryKey) {
    const country = countriesData[countryKey];

    // تحديث العلم واسم الدولة في المستطيل الرئيسي
    document.getElementById('selectedFlag').textContent = country.flag;
    document.getElementById('selectedCountry').textContent = country.name;

    // إخفاء القائمة
    document.getElementById('countryOptions').style.display = 'none';
    document.getElementById('dropdownArrow').style.transform = 'rotate(0deg)';

    console.log('تم اختيار الدولة:', countryKey);
}

// إغلاق القائمة عند النقر خارجها
document.addEventListener('click', function(event) {
    const dropdown = document.querySelector('.country-selector');
    if (dropdown && !dropdown.contains(event.target)) {
        document.getElementById('countryOptions').style.display = 'none';
        document.getElementById('dropdownArrow').style.transform = 'rotate(0deg)';
    }
});
</script>
</body>
</html>
</html>
