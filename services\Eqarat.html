<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الخدمات - Get Me</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* منع التمرير الأفقي غير المرغوب فيه */
        html, body {
            overflow-x: hidden;
            max-width: 100%;
        }

        /* منع التمرير الأفقي للحاويات الرئيسية */
        .main-content {
        /* كارت منشور البائع */
        .seller-post-card {
            border: 1.2px solid #e4e6ea;
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .seller-post-card:hover {
            border-color: #16CCC8;
            box-shadow: 0 4px 16px rgba(34,127,204,0.10);
        }
            overflow-x: hidden;
            max-width: 100%;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            direction: rtl;
            color: #333;
        }

        /* الشريط العلوي */
        .header {
            background: linear-gradient(90deg, #16CCC8, #227FCC);
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 1000 !important;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100% !important;
        }

        .app-name {
            font-size: 40px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
            max-width: 600px;
            margin: 0 30px;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 12px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.15);
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            outline: none;
            color: white;
            transition: all 0.3s ease;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
            font-weight: 500;
        }

        .search-input:focus {
            outline: none;
        }

        .search-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: white;
            font-size: 24px;
            padding: 10px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-icon {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            position: relative;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        /* شريط التنقل */
        .nav-bar {
            background: white;
            position: fixed !important;
            top: 70px !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 999 !important;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            width: 100% !important;
        }

        .nav-items {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 25px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.3s;
            color: #495057;
            font-weight: 500;
            position: relative;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            color: #16CCC8;
            transform: translateY(-2px);
        }

        .nav-item:hover i {
            transform: scale(1.1);
            color: #16CCC8;
        }

        .nav-item i {
            font-size: 24px;
            transition: all 0.3s ease;
        }

        .nav-item.home-item {
            margin-left: 80px;
        }

        .nav-item.home-item i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item.home-item:hover i,
        .nav-item.home-item.active i {
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item.location-item {
            margin-right: auto;
            background: rgba(22, 204, 200, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(22, 204, 200, 0.3);
        }

        .nav-item.location-item:hover {
            background: rgba(22, 204, 200, 0.2);
            border-color: rgba(22, 204, 200, 0.5);
        }

        .nav-item.has-notification i {
            color: #ff0000 !important;
            -webkit-text-fill-color: #ff0000 !important;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #ff0000;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 130px;
            padding: 20px;
        }

        /* إخفاء شريط التمرير */
        #sellersStoriesContainer::-webkit-scrollbar,
        #liveStreamsContainer::-webkit-scrollbar,
        #premiumOffersContainer::-webkit-scrollbar {
            display: none;
        }

        /* تحسين تأثيرات القصص */
        .sellers-stories h3:hover {
            color: #16CCC8 !important;
            transition: all 0.3s ease;
        }

        /* تأثيرات الأسهم */
        #storiesLeftArrow:hover, #storiesRightArrow:hover {
            background: rgba(22, 204, 200, 1) !important;
            transform: translateY(-50%) scale(1.1) !important;
            box-shadow: 0 4px 12px rgba(22, 204, 200, 0.4) !important;
        }

        /* تأثيرات أسهم البث المباشر */
        #liveLeftArrow:hover, #liveRightArrow:hover {
            background: rgba(231, 76, 60, 1) !important;
            transform: translateY(-50%) scale(1.1) !important;
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.6) !important;
        }

        /* تأثير النبض لعلامة LIVE */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* تأثير الشيمر للعروض المميزة */
        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* منشورات البروفايل */
        .my-posts-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .post-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .post-card:hover {
            border: 2px solid #16CCC8;
            box-shadow: 0 8px 25px rgba(22, 204, 200, 0.15);
        }

        .post-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .post-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
        }

        .post-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .post-info h4 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .post-time {
            color: #666;
            font-size: 12px;
        }

        .post-content {
            color: #333;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .post-actions {
            display: flex;
            justify-content: space-around;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .post-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            font-size: 14px;
        }

        .post-action:hover {
            background: rgba(22, 204, 200, 0.1);
            color: #16CCC8;
        }

        .post-action i {
            font-size: 16px;
        }



        /* شريط القصص */
        .stories-section {
            margin-bottom: 20px;
            position: relative;
        }

        .stories-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }

        .stories-wrapper {
            display: flex;
            gap: 15px;
            transition: transform 1s ease;
            width: fit-content;
        }

        .story-card {
            width: 120px;
            height: 180px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            cursor: pointer;
            background: white;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            position: relative;
        }

        .story-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .story-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            color: white;
            font-size: 24px;
        }

        .story-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .story-name {
            height: 60px;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: 600;
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
        }

        /* أسهم التنقل */
        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            z-index: 10;
            transition: all 0.3s;
        }

        .nav-arrow:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%);
        }

        .nav-arrow.prev {
            right: 10px;
        }

        .nav-arrow.next {
            left: 10px;
        }

        /* تنسيق قائمة اختيار الدولة */
        .country-selector {
            font-family: 'Cairo', sans-serif;
        }

        .country-selector #countryOptions {
            scrollbar-width: thin;
            scrollbar-color: #16CCC8 #f1f1f1;
        }

        .country-selector #countryOptions::-webkit-scrollbar {
            width: 6px;
        }

        .country-selector #countryOptions::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .country-selector #countryOptions::-webkit-scrollbar-thumb {
            background: #16CCC8;
            border-radius: 10px;
        }

        .country-selector #countryOptions::-webkit-scrollbar-thumb:hover {
            background: #227FCC;
        }

        /* تأثير الانتقال للقائمة */
        .country-selector #countryOptions {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تنسيق الأعلام */
        .country-flag {
            display: inline-block;
            width: 20px;
            height: 15px;
            border-radius: 2px;
            border: 1px solid #ddd;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* تحسين عرض الأعلام في القائمة */
        .country-selector #countryOptions > div:hover .country-flag {
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            transform: scale(1.05);
        }
    </style>
</head>
<body dir="rtl">
    
 <!-- الشريط العلوي -->
<div class="header">
    <div class="user-section">
        <div class="menu-icon">
            <i class="fas fa-bars"></i>
        </div>
        <div class="user-avatar">
            <img src="HusseinNihad.png" alt="المستخدم" onerror="this.style.display='none'">
        </div>
        <div class="user-name">حسين نهاد</div>
    </div>
    <div class="search-section">
        <input type="text" class="search-input" placeholder="ابحث عن خدمة أو منتج...">
        <button class="search-btn">
            <i class="fas fa-search"></i>
        </button>
    </div>
    <div class="app-name">Get Me</div>
</div>


    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item home-item active">
              <a href="Home.html">
  <i class="fas fa-home"></i>
</a>

                <span>الرئيسية</span>
            </div>
            <div class="nav-item has-notification">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge">3</div>
            </div>
            <div class="nav-item">
                <i class="fas fa-comments"></i>
                <span>الرسائل</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
            <div class="nav-item location-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>بغداد، العراق</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- عنوان القصص -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-circle" style="color: #16CCC8; margin-left: 8px;"></i>
                قصص البائعين
            </h3>
        </div>

        <!-- شريط القصص للبائعين -->
        <div class="sellers-stories" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <!-- زر إنشاء قصة داخل الشريط من اليمين -->
            <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;">
                <button onclick="createNewStory()" style="
                    background: linear-gradient(45deg, #16CCC8, #227FCC);
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(22, 204, 200, 0.3);
                " onmouseover="
                    this.style.transform='translateY(-2px)';
                    this.style.boxShadow='0 4px 12px rgba(22, 204, 200, 0.5)';
                " onmouseout="
                    this.style.transform='translateY(0)';
                    this.style.boxShadow='0 2px 8px rgba(22, 204, 200, 0.3)';
                ">
                    <i class="fas fa-plus" style="margin-left: 5px;"></i>
                    إنشاء قصة جديدة
                </button>
            </div>
            <div style="position: relative;">
                <!-- سهم التمرير الأيسر -->
                <button id="storiesLeftArrow" onclick="scrollStories('left')" style="
                    position: absolute;
                    left: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="storiesRightArrow" onclick="scrollStories('right')" style="
                    position: absolute;
                    right: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="sellersStoriesContainer" style="
                    display: flex;
                    gap: 15px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 10px 15px;
                    height: 95px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                    align-items: center;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- عنوان البث المباشر -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-video" style="color: #e74c3c; margin-left: 8px;"></i>
                البث المباشر
            </h3>
        </div>

        <!-- شريط البث المباشر -->
        <div class="live-streams" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <!-- زر بدء بث مباشر داخل الشريط من اليمين -->
            <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;">
                <button onclick="startLiveStream()" style="
                    background: linear-gradient(45deg, #8e44ad, #e74c3c, #f39c12);
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
                " onmouseover="
                    this.style.transform='translateY(-2px)';
                    this.style.boxShadow='0 4px 12px rgba(231, 76, 60, 0.5)';
                " onmouseout="
                    this.style.transform='translateY(0)';
                    this.style.boxShadow='0 2px 8px rgba(231, 76, 60, 0.3)';
                ">
                    <i class="fas fa-video" style="margin-left: 5px;"></i>
                    بدء بث مباشر
                </button>
            </div>
            <div style="position: relative;">
                <!-- سهم التمرير الأيسر -->
                <button id="liveLeftArrow" onclick="scrollLiveStreams('left')" style="
                    position: absolute;
                    left: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(231, 76, 60, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="liveRightArrow" onclick="scrollLiveStreams('right')" style="
                    position: absolute;
                    right: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(231, 76, 60, 0.9);
                    border: none;
                    color: white;
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="liveStreamsContainer" style="
                    display: flex;
                    gap: 15px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 10px 15px;
                    height: 95px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                    align-items: center;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- عنوان العروض المميزة -->
        <div style="margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333; font-size: 16px;">
                <i class="fas fa-star" style="color: #f39c12; margin-left: 8px;"></i>
                العروض المميزة - عقارات
            </h3>
        </div>

        <!-- شريط العروض المميزة -->
        <div class="premium-offers-section" style="
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        ">
            <div class="premium-offers-container" style="
                position: relative;
                overflow: hidden;
                padding: 10px 0;
            ">
                <!-- سهم التمرير الأيسر -->
                <button id="premiumLeftArrow" onclick="scrollPremiumOffers('left')" style="
                    position: absolute;
                    left: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 45px;
                    height: 45px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <!-- سهم التمرير الأيمن -->
                <button id="premiumRightArrow" onclick="scrollPremiumOffers('right')" style="
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(22, 204, 200, 0.9);
                    border: none;
                    color: white;
                    width: 45px;
                    height: 45px;
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 10;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <div id="premiumOffersContainer" style="
                    display: flex;
                    gap: 20px;
                    overflow-x: auto;
                    scroll-behavior: smooth;
                    padding: 0 60px;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                ">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- قسم المحتوى السفلي -->
    <div style="display: flex; gap: 25px; align-items: flex-start; flex-direction: row;">
            <!-- مستطيل العروض الجانبية (تحليل السوق، الأسعار، المشاريع) في الجهة اليسرى -->
            <div style="width: 420px; max-width: 100%; display: flex; flex-direction: column; gap: 18px; order: 2;">
                <div id="marketAnalysisWidget">
                    <div style="background: white; border-radius: 20px; padding: 24px; margin-bottom: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.10); border: 1px solid #e4e6ea; width: 100%;">
                        <div style="text-align: center; margin-bottom: 12px; font-size: 15px;">
                            <h4 style="margin: 0; color: #333; font-size: 22px; font-weight: bold;">
                                <i class="fas fa-chart-line" style="color: #16CCC8; margin-left: 10px; font-size: 24px;"></i>
                                تحليل السوق العقاري
                            </h4>
                        </div>
                        <!-- قائمة اختيار الدولة -->
                        <div style="text-align: center; margin-bottom: 18px;">
                            <div class="country-selector" style="position: relative; display: inline-block; width: 200px;">
                                <div id="countryDropdown" onclick="toggleCountryDropdown()" style="
                                    background: white;
                                    border: 2px solid #e4e6ea;
                                    border-radius: 15px;
                                    padding: 12px 15px;
                                    cursor: pointer;
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    transition: all 0.3s ease;
                                    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                                " onmouseover="this.style.borderColor='#16CCC8'" onmouseout="this.style.borderColor='#e4e6ea'">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span id="selectedFlag" style="font-size: 20px; width: 25px; text-align: center;">🇮🇶</span>
                                        <span id="selectedCountry" style="font-weight: 600; color: #333;">العراق</span>
                                    </div>
                                    <i class="fas fa-chevron-down" id="dropdownArrow" style="color: #666; transition: transform 0.3s ease;"></i>
                                </div>
                                <div id="countryOptions" style="
                                    position: absolute;
                                    top: 100%;
                                    left: 0;
                                    right: 0;
                                    background: white;
                                    border: 2px solid #e4e6ea;
                                    border-radius: 15px;
                                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                                    z-index: 1000;
                                    max-height: 300px;
                                    overflow-y: auto;
                                    display: none;
                                    margin-top: 5px;
                                ">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                        <div style="text-align: center; margin-bottom: 18px;">
                            <div style="display: inline-flex; background: #f8f9fa; border-radius: 25px; padding: 5px; border: 1px solid #e4e6ea; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px;">أسبوعي</button>
                                <button style="background: transparent; border: none; color: #666; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px;">شهري</button>
                                <button style="background: transparent; border: none; color: #666; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: bold; margin: 0 3px;">سنوي</button>
                            </div>
                        </div>
                        <div style="background: #fafbfc; border: 1px solid #e4e6ea; border-radius: 15px; padding: 18px; margin-bottom: 15px;">
                            <div style="color: #333; font-size: 16px; font-weight: bold; margin-bottom: 10px; text-align: center;">📊 الأداء الأسبوعي</div>
                            <div style="font-size: 28px; font-weight: bold; color: #16CCC8; margin-bottom: 5px;">+12%</div>
                            <div style="font-size: 16px; opacity: 0.9; color: #333;">هذا الأسبوع</div>
                            <div style="font-size: 14px; opacity: 0.8; margin-top: 5px; color: #666;">📈 نمو مستمر في السوق</div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px;">
                            <div style="background: #e8f5e8; border: 1px solid #c3e6c3; padding: 12px; border-radius: 10px; text-align: center;">
                                <div style="color: #27ae60; font-size: 18px; margin-bottom: 4px;"><i class="fas fa-home"></i></div>
                                <div style="color: #27ae60; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+8%</div>
                                <div style="color: #666; font-size: 10px;">الشقق</div>
                            </div>
                            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 12px; border-radius: 10px; text-align: center;">
                                <div style="color: #f39c12; font-size: 18px; margin-bottom: 4px;"><i class="fas fa-map-marked-alt"></i></div>
                                <div style="color: #f39c12; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+15%</div>
                                <div style="color: #666; font-size: 10px;">الأراضي</div>
                            </div>
                            <div style="background: #ffeaa7; border: 1px solid #fdcb6e; padding: 12px; border-radius: 10px; text-align: center;">
                                <div style="color: #e67e22; font-size: 18px; margin-bottom: 4px;"><i class="fas fa-building"></i></div>
                                <div style="color: #e67e22; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+5%</div>
                                <div style="color: #666; font-size: 10px;">التجاري</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="realEstatePricesWidget">
                    <div style="background: white; border-radius: 15px; padding: 18px; margin-bottom: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.10); width: 100%;">
                        <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">
                            <i class="fas fa-map-marked-alt" style="color: #e74c3c; margin-left: 8px;"></i>
                            أسعار العقارات - هذا الأسبوع
                        </h4>
                        <div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;">
                                <div>
                                    <div style="font-weight: bold; color: #333; font-size: 13px;">بغداد</div>
                                    <div style="color: #666; font-size: 11px;">متوسط السعر</div>
                                </div>
                                <div style="text-align: left;">
                                    <div style="font-weight: bold; color: #16CCC8; font-size: 13px;">180,000$</div>
                                    <div style="color: #27ae60; font-size: 11px;"><i class="fas fa-arrow-up"></i> 8%</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;">
                                <div>
                                    <div style="font-weight: bold; color: #333; font-size: 13px;">البصرة</div>
                                    <div style="color: #666; font-size: 11px;">متوسط السعر</div>
                                </div>
                                <div style="text-align: left;">
                                    <div style="font-weight: bold; color: #16CCC8; font-size: 13px;">120,000$</div>
                                    <div style="color: #27ae60; font-size: 11px;"><i class="fas fa-arrow-up"></i> 12%</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;">
                                <div>
                                    <div style="font-weight: bold; color: #333; font-size: 13px;">أربيل</div>
                                    <div style="color: #666; font-size: 11px;">متوسط السعر</div>
                                </div>
                                <div style="text-align: left;">
                                    <div style="font-weight: bold; color: #16CCC8; font-size: 13px;">150,000$</div>
                                    <div style="color: #27ae60; font-size: 11px;"><i class="fas fa-arrow-up"></i> 5%</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;">
                                <div>
                                    <div style="font-weight: bold; color: #333; font-size: 13px;">النجف</div>
                                    <div style="color: #666; font-size: 11px;">متوسط السعر</div>
                                </div>
                                <div style="text-align: left;">
                                    <div style="font-weight: bold; color: #16CCC8; font-size: 13px;">95,000$</div>
                                    <div style="color: #27ae60; font-size: 11px;"><i class="fas fa-arrow-up"></i> 15%</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;">
                                <div>
                                    <div style="font-weight: bold; color: #333; font-size: 13px;">كربلاء</div>
                                    <div style="color: #666; font-size: 11px;">متوسط السعر</div>
                                </div>
                                <div style="text-align: left;">
                                    <div style="font-weight: bold; color: #16CCC8; font-size: 13px;">110,000$</div>
                                    <div style="color: #27ae60; font-size: 11px;"><i class="fas fa-arrow-up"></i> 7%</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;">
                                <div>
                                    <div style="font-weight: bold; color: #333; font-size: 13px;">الموصل</div>
                                    <div style="color: #666; font-size: 11px;">متوسط السعر</div>
                                </div>
                                <div style="text-align: left;">
                                    <div style="font-weight: bold; color: #16CCC8; font-size: 13px;">85,000$</div>
                                    <div style="color: #e74c3c; font-size: 11px;"><i class="fas fa-arrow-down"></i> 3%</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;">
                                <div>
                                    <div style="font-weight: bold; color: #333; font-size: 13px;">السليمانية</div>
                                    <div style="color: #666; font-size: 11px;">متوسط السعر</div>
                                </div>
                                <div style="text-align: left;">
                                    <div style="font-weight: bold; color: #16CCC8; font-size: 13px;">130,000$</div>
                                    <div style="color: #27ae60; font-size: 11px;"><i class="fas fa-arrow-up"></i> 9%</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px; margin-bottom: 6px;">
                                <div>
                                    <div style="font-weight: bold; color: #333; font-size: 13px;">ديالى</div>
                                    <div style="color: #666; font-size: 11px;">متوسط السعر</div>
                                </div>
                                <div style="text-align: left;">
                                    <div style="font-weight: bold; color: #16CCC8; font-size: 13px;">75,000$</div>
                                    <div style="color: #27ae60; font-size: 11px;"><i class="fas fa-arrow-up"></i> 4%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="govProjectsWidget">
                    <div style="background: white; border-radius: 15px; padding: 18px; box-shadow: 0 4px 15px rgba(0,0,0,0.10); width: 100%;">
                        <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">
                            <i class="fas fa-building" style="color: #f39c12; margin-left: 8px;"></i>
                            المشاريع الحكومية
                        </h4>
                        <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 10px; border-radius: 10px; text-align: center; margin-bottom: 10px;">
                            <div style="font-size: 16px; font-weight: bold; margin-bottom: 3px;">
                                <i class="fas fa-handshake"></i>
                                فرص استثمارية
                            </div>
                            <div style="font-size: 13px; opacity: 0.9;">مشاريع تبحث عن مستثمرين</div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 10px; margin-bottom: 10px; background: #fafafa;">
                            <h5 style="margin: 0 0 5px 0; color: #333; font-size: 13px;">مشروع المدينة الذكية - بغداد</h5>
                            <p style="margin: 0 0 7px 0; color: #666; font-size: 11px; line-height: 1.4;">مشروع سكني متكامل يحتاج مستثمرين ومقاولين للبناء والتطوير</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 12px;">500 مليون دولار</div>
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 4px 10px; border-radius: 15px; cursor: pointer; font-size: 10px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 10px; margin-bottom: 10px; background: #fafafa;">
                            <h5 style="margin: 0 0 5px 0; color: #333; font-size: 13px;">مجمع تجاري - البصرة</h5>
                            <p style="margin: 0 0 7px 0; color: #666; font-size: 11px; line-height: 1.4;">مجمع تجاري كبير في منطقة استراتيجية يبحث عن شركاء استثماريين</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 12px;">200 مليون دولار</div>
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 4px 10px; border-radius: 15px; cursor: pointer; font-size: 10px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 10px; margin-bottom: 10px; background: #fafafa;">
                            <h5 style="margin: 0 0 5px 0; color: #333; font-size: 13px;">مشروع الإسكان الشعبي</h5>
                            <p style="margin: 0 0 7px 0; color: #666; font-size: 11px; line-height: 1.4;">بناء وحدات سكنية للطبقة المتوسطة في عدة محافظات</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 12px;">300 مليون دولار</div>
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 4px 10px; border-radius: 15px; cursor: pointer; font-size: 10px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                        <div style="border: 1px solid #eee; border-radius: 10px; padding: 10px; margin-bottom: 0; background: #fafafa;">
                            <h5 style="margin: 0 0 5px 0; color: #333; font-size: 13px;">تطوير المنطقة الصناعية</h5>
                            <p style="margin: 0 0 7px 0; color: #666; font-size: 11px; line-height: 1.4;">تطوير وتحديث المنطقة الصناعية وإنشاء مصانع جديدة</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #f39c12; font-weight: bold; font-size: 12px;">150 مليون دولار</div>
                                <button style="background: linear-gradient(135deg, #16CCC8, #227FCC); border: none; color: white; padding: 4px 10px; border-radius: 15px; cursor: pointer; font-size: 10px;">تفاصيل أكثر</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- مستطيل منشورات البائعين (شبكة 2 عمود) في الجهة اليمنى -->
            <div style="flex: 2; background: white; border-radius: 18px; padding: 28px 18px; box-shadow: 0 6px 20px rgba(0,0,0,0.10); margin-bottom: 0; order: 0;">
                <div style="margin-bottom: 22px;">
                    <h3 style="margin: 0; color: #333; font-size: 28px;">
                        <i class="fas fa-newspaper" style="color: #16CCC8; margin-left: 8px;"></i>
                        منشورات البائعين
                    </h3>
                </div>
                <div id="sellersPostsContainer" class="my-posts-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 22px; font-size: 18px;">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
        </div>
                    <div style="width: 15px; height: 45%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 90%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 70%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 85%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                    <div style="width: 15px; height: 95%; background: linear-gradient(to top, #16CCC8, #227FCC); border-radius: 3px;"></div>
                </div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 10px; color: #666;">
                <span>السبت</span><span>الأحد</span><span>الاثنين</span><span>الثلاثاء</span><span>الأربعاء</span><span>الخميس</span><span>الجمعة</span>
            </div>
        </div>

        <!-- مؤشر الأداء الرئيسي -->
        <div style="
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 15px;
        ">
            <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">+12%</div>
            <div style="font-size: 14px; opacity: 0.9;">هذا الأسبوع</div>
            <div style="font-size: 12px; opacity: 0.8; margin-top: 3px;">📈 نمو مستمر</div>
        </div>

        <!-- الإحصائيات التفصيلية -->
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px;">
            <div style="
                background: #e8f5e8;
                border: 1px solid #c3e6c3;
                padding: 12px;
                border-radius: 10px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
            " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                <div style="color: #27ae60; font-size: 18px; margin-bottom: 4px;">
                    <i class="fas fa-home"></i>
                </div>
                <div style="color: #27ae60; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+8%</div>
                <div style="color: #666; font-size: 10px;">الشقق</div>
            </div>

            <div style="
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 12px;
                border-radius: 10px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
            " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                <div style="color: #e67e22; font-size: 18px; margin-bottom: 4px;">
                    <i class="fas fa-building"></i>
                </div>
                <div style="color: #e67e22; font-weight: bold; font-size: 14px; margin-bottom: 2px;">+5%</div>
                <div style="color: #666; font-size: 10px;">التجاري</div>
            </div>
        </div>
    </div>

    <script>
        // تحميل قصص البائعين
        function loadSellersStories(serviceName) {
            const container = document.getElementById('sellersStoriesContainer');
            const stories = generateSellersStories(serviceName);

            container.innerHTML = stories.map(story => `
                <div style="
                    min-width: 75px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                    <div style="
                        width: 65px;
                        height: 65px;
                        border-radius: 50%;
                        background: linear-gradient(45deg, #16CCC8, #227FCC);
                        padding: 3px;
                        margin: 0 auto 8px;
                        box-shadow: 0 4px 15px rgba(22, 204, 200, 0.3);
                        transition: all 0.3s ease;
                    ">
                        <div style="
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            background: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            overflow: hidden;
                        ">
                            <img src="${story.avatar}" alt="${story.name}" style="
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                                border-radius: 50%;
                                transition: all 0.3s ease;
                            " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="
                                display: none;
                                width: 100%;
                                height: 100%;
                                align-items: center;
                                justify-content: center;
                                background: linear-gradient(45deg, #16CCC8, #227FCC);
                                color: white;
                                font-size: 20px;
                                border-radius: 50%;
                            ">
                                <i class="${story.icon}"></i>
                            </div>
                        </div>
                    </div>
                    <div style="
                        font-size: 11px;
                        color: #333;
                        font-weight: 600;
                        line-height: 1.2;
                        max-width: 75px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    ">
                        ${story.name.length > 8 ? story.name.substring(0, 8) + '...' : story.name}
                    </div>
                </div>
            `).join('');
        }

        // إنشاء قصص البائعين العقاريين فقط
        function generateSellersStories(serviceName) {
            const stories = [];
            const icons = [
                'fas fa-home', 'fas fa-building', 'fas fa-city', 'fas fa-home', 'fas fa-map-marked-alt',
                'fas fa-hammer', 'fas fa-building', 'fas fa-key', 'fas fa-crown', 'fas fa-chess-rook',
                'fas fa-chart-line', 'fas fa-home', 'fas fa-store', 'fas fa-tools', 'fas fa-mosque',
                'fas fa-seedling', 'fas fa-building', 'fas fa-users', 'fas fa-globe', 'fas fa-water',
                'fas fa-home', 'fas fa-building', 'fas fa-city', 'fas fa-home', 'fas fa-map-marked-alt',
                'fas fa-hammer'
            ];
            for (let i = 0; i < 26; i++) {
                const letter = String.fromCharCode(65 + i); // 'A' to 'Z'
                stories.push({
                    name: `بائع ${letter}`,
                    avatar: '../Eqarat/Stories/' + letter + '.jpg',
                    icon: icons[i % icons.length]
                });
            }
            return stories;
        }

        // إنشاء البث المباشر العقاري
        function generateLiveStreams(serviceName) {
            const streams = [];

            // بث مباشر للعقارات (15 بث)
            streams.push(
                { name: 'عقارات الذهبية', avatar: '../Eqarat/Live/1.jpg', icon: 'fas fa-home', isLive: true },
                { name: 'مكتب الإسكان', avatar: '../Eqarat/Live/2.jpg', icon: 'fas fa-building', isLive: false },
                { name: 'بغداد الحديثة', avatar: '../Eqarat/Live/3.jpg', icon: 'fas fa-city', isLive: true },
                { name: 'عقارات الكرادة', avatar: '../Eqarat/Live/4.jpg', icon: 'fas fa-home', isLive: true },
                { name: 'الأراضي الاستثمارية', avatar: '../Eqarat/Live/5.jpg', icon: 'fas fa-map-marked-alt', isLive: false },
                { name: 'البناء الحديث', avatar: '../Eqarat/Live/6.jpg', icon: 'fas fa-hammer', isLive: true },
                { name: 'عقارات الجادرية', avatar: '../Eqarat/Live/7.jpg', icon: 'fas fa-building', isLive: false },
                { name: 'الشقق الفاخرة', avatar: '../Eqarat/Live/8.jpg', icon: 'fas fa-key', isLive: true },
                { name: 'المنصور الراقية', avatar: '../Eqarat/Live/9.jpg', icon: 'fas fa-crown', isLive: false },
                { name: 'الفلل الفاخرة', avatar: '../Eqarat/Live/10.jpg', icon: 'fas fa-chess-rook', isLive: true },
                { name: 'الاستثمار العقاري', avatar: '../Eqarat/Live/11.jpg', icon: 'fas fa-chart-line', isLive: true },
                { name: 'عقارات الزعفرانية', avatar: '../Eqarat/Live/12.jpg', icon: 'fas fa-home', isLive: false },
                { name: 'العقارات التجارية', avatar: '../Eqarat/Live/13.jpg', icon: 'fas fa-store', isLive: true },
                { name: 'التطوير العقاري', avatar: '../Eqarat/Live/14.jpg', icon: 'fas fa-tools', isLive: false },
                { name: 'عقارات الكاظمية', avatar: '../Eqarat/Live/15.jpg', icon: 'fas fa-mosque', isLive: true }
            );

            return streams;
        }

        // إنشاء العروض المميزة العقارية
        function generatePremiumOffers(serviceName) {
            // صور العروض من مجلد Special-offers (EA.jpg إلى ET.jpg)
            const imageNames = ['EA', 'EB', 'EC', 'ED', 'EE', 'EF', 'EG', 'EH', 'EI', 'EJ', 'EK', 'EL', 'EM', 'EN', 'EO', 'EP', 'EQ', 'ER', 'ES', 'ET'];
            const offers = [];
            for (let i = 0; i < imageNames.length; i++) {
                offers.push({
                    title: `عرض مميز ${i + 1}`,
                    description: `تفاصيل العرض المميز رقم ${i + 1}`,
                    price: `${(i + 1) * 10000}$`,
                    seller: `بائع ${i + 1}`,
                    icon: 'fas fa-star',
                    image: '../Eqarat/Special-offers/' + imageNames[i] + '.jpg'
                });
            }
            return offers;
        }

        // إنشاء منشورات البائعين من دول عربية مختلفة
        function generateSellersPosts(serviceName) {
            const posts = [];
            for (let i = 1; i <= 34; i++) {
                posts.push({
                    id: i,
                    author: `بائع ${i}`,
                    time: `منذ ${i} ساعة`,
                    country: 'العراق',
                    countryFlag: '🇮🇶',
                    countryName: 'العراق',
                    countryColor: '#007A3D',
                    content: `فرصة عقارية رائعة للبيع أو الإيجار. لمزيد من التفاصيل تواصل معنا مباشرة!`,
                    images: [`../Eqarat/Publications/F${i}.jpg`],
                    avatar: `../Eqarat/Publications/F${i}.jpg`,
                    likes: 100 + i * 3,
                    comments: 20 + i,
                    shares: 5 + i
                });
            }
            return posts;
        }

        // دوال التمرير للقصص
        function scrollStories(direction) {
            const container = document.getElementById('sellersStoriesContainer');
            const scrollAmount = 400; // زيادة مسافة التمرير لتناسب العدد الكبير

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updateArrowsState();
            }, 400);
        }

        // تحديث حالة الأسهم
        function updateArrowsState() {
            const container = document.getElementById('sellersStoriesContainer');
            const leftArrow = document.getElementById('storiesLeftArrow');
            const rightArrow = document.getElementById('storiesRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دوال التمرير للبث المباشر
        function scrollLiveStreams(direction) {
            const container = document.getElementById('liveStreamsContainer');
            const scrollAmount = 400;

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updateLiveArrowsState();
            }, 400);
        }

        // تحديث حالة أسهم البث المباشر
        function updateLiveArrowsState() {
            const container = document.getElementById('liveStreamsContainer');
            const leftArrow = document.getElementById('liveLeftArrow');
            const rightArrow = document.getElementById('liveRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دالة بدء بث مباشر
        function startLiveStream() {
            alert('🔴 بدء بث مباشر!\n\nسيتم فتح نافذة البث المباشر...\n\n📹 يمكنك بث فيديو مباشر\n💬 التفاعل مع المشاهدين\n📊 مراقبة الإحصائيات');
        }

        // دوال التمرير للعروض المميزة
        function scrollPremiumOffers(direction) {
            const container = document.getElementById('premiumOffersContainer');
            const scrollAmount = 400;

            if (direction === 'left') {
                container.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                container.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأسهم
            setTimeout(() => {
                updatePremiumArrowsState();
            }, 400);
        }

        // تحديث حالة أسهم العروض المميزة
        function updatePremiumArrowsState() {
            const container = document.getElementById('premiumOffersContainer');
            const leftArrow = document.getElementById('premiumLeftArrow');
            const rightArrow = document.getElementById('premiumRightArrow');

            // إخفاء السهم الأيسر إذا كنا في البداية
            if (container.scrollLeft <= 0) {
                leftArrow.style.opacity = '0.3';
                leftArrow.style.cursor = 'not-allowed';
            } else {
                leftArrow.style.opacity = '1';
                leftArrow.style.cursor = 'pointer';
            }

            // إخفاء السهم الأيمن إذا كنا في النهاية
            if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
                rightArrow.style.opacity = '0.3';
                rightArrow.style.cursor = 'not-allowed';
            } else {
                rightArrow.style.opacity = '1';
                rightArrow.style.cursor = 'pointer';
            }
        }

        // دالة فتح العرض المميز
        function openServicePremiumOffer(title) {
            alert('⭐ عرض مميز!\n\n' + title + '\n\nسيتم فتح تفاصيل العرض...\n\n💰 معلومات السعر\n📞 تفاصيل التواصل\n📍 الموقع على الخريطة');
        }

        // تحميل منشورات البائعين (تصميم Profile.html)
        function loadSellersPosts(serviceName) {
            const container = document.getElementById('sellersPostsContainer');
            const posts = generateSellersPosts(serviceName);

            // أسماء للبائعين (عددها 34)
            const sellerNames = [
                "أحمد العاني", "سارة الجبوري", "محمد السعدي", "ليلى الكعبي", "علي الموسوي", "نور الزبيدي", "حسن التميمي", "زينب الراوي", "يوسف الدليمي", "مريم الشمري", "عمر الفهداوي", "هدى عبد الله", "سيف الجنابي", "آية الساعدي", "كرار الحسيني", "شهد العطار", "حيدر عبد الكريم", "بتول عبد الرضا", "مصطفى عبد الله", "إيمان عبد الجبار", "حسين كاظم", "سندس عبد الأمير", "عمار عبد الحسين", "شيماء عبد الله", "حسنين عبد الرضا", "زينب عبد الكريم", "محمد عبد الجبار", "سارة عبد الحسين", "علي عبد الله", "نور عبد الرضا", "حسن عبد الجبار", "ليلى عبد الحسين", "يوسف عبد الكريم", "مريم عبد الله"
            ];
            container.innerHTML = posts.map((post, i) => `
                <div class="seller-post-card">
                    <div class="post-header">
                        <div class="post-avatar">
                            <img src="${post.avatar}" alt="${sellerNames[i] || post.author}">
                        </div>
                        <div class="post-info">
                            <h4>${sellerNames[i] || post.author}</h4>
                            <div class="post-time">${post.time} • ${post.country}</div>
                        </div>
                        <div style="
                            background: ${post.countryColor};
                            color: white;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: bold;
                        ">
                            ${post.countryFlag} ${post.countryName}
                        </div>
                    </div>
                    <div class="post-content">${post.content}</div>
                    ${post.images && post.images.length > 0 ? `
                        <div class="post-images" style="margin: 15px 0; display: ${post.images.length > 1 ? 'grid' : 'block'}; ${post.images.length > 1 ? 'grid-template-columns: 1fr 1fr; gap: 10px;' : ''}">
                            ${post.images.map(img => `
                                <img src="${img}" alt="صورة المنشور" style="
                                    width: 100%;
                                    height: ${post.images.length > 1 ? '200px' : '250px'};
                                    object-fit: cover;
                                    border-radius: 10px;
                                    cursor: pointer;
                                " onclick="openImageModal('${img}')">
                            `).join('')}
                        </div>
                    ` : ''}
                    <div class="post-stats" style="display: flex; justify-content: space-around; padding: 10px 0; border-top: 1px solid #f0f0f0; border-bottom: 1px solid #f0f0f0; margin: 15px 0; font-size: 14px; color: #666;">
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-heart" style="color: #16CCC8;"></i>
                            <span>${post.likes}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-comment" style="color: #16CCC8;"></i>
                            <span>${post.comments}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-share" style="color: #16CCC8;"></i>
                            <span>${post.shares}</span>
                        </div>
                    </div>
                    <div class="post-actions">
                        <div class="post-action" onclick="likePost(${post.id})">
                            <i class="fas fa-heart"></i>
                            <span>إعجاب</span>
                        </div>
                        <div class="post-action" onclick="commentPost(${post.id})">
                            <i class="fas fa-comment"></i>
                            <span>تعليق</span>
                        </div>
                        <div class="post-action" onclick="sharePost(${post.id})">
                            <i class="fas fa-share"></i>
                            <span>مشاركة</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // تحميل العروض المميزة
        function loadPremiumOffers(serviceName) {
            const container = document.getElementById('premiumOffersContainer');
            const offers = generatePremiumOffers(serviceName);

            container.innerHTML = offers.map(offer => `
                <div class="premium-offer-card" onclick="openServicePremiumOffer('${offer.title}')" style="
                    width: 380px;
                    height: 200px;
                    border-radius: 15px;
                    border: 3px solid #FFD700;
                    background: linear-gradient(135deg, #FFF8DC, #FFFACD);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    cursor: pointer;
                    transition: all 0.3s;
                    position: relative;
                    flex-shrink: 0;
                    overflow: hidden;
                ">
                    <div style="
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
                        animation: shimmer 3s infinite;
                    "></div>

                    <div style="
                        width: 100%;
                        height: 100%;
                        position: relative;
                        z-index: 2;
                        overflow: hidden;
                        border-radius: 12px;
                    ">
                        <img src="${offer.image}" alt="${offer.title}" style="
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            border-radius: 12px;
                        " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="
                            display: none;
                            width: 100%;
                            height: 100%;
                            background: linear-gradient(135deg, #16CCC8, #227FCC);
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 48px;
                            border-radius: 12px;
                        ">
                            <i class="${offer.icon}"></i>
                        </div>

                        <div style="
                            position: absolute;
                            top: 0;
                            right: 0;
                            background: linear-gradient(135deg, rgba(0,0,0,0.5), rgba(0,0,0,0.3));
                            color: white;
                            padding: 15px;
                            z-index: 3;
                            border-radius: 0 12px 0 15px;
                            max-width: 70%;
                        ">
                            <div style="
                                font-size: 14px;
                                font-weight: bold;
                                margin-bottom: 3px;
                                text-shadow: 2px 2px 4px rgba(0,0,0,0.9);
                                line-height: 1.2;
                            ">${offer.title}</div>
                            <div style="
                                font-size: 11px;
                                opacity: 0.95;
                                text-shadow: 1px 1px 3px rgba(0,0,0,0.9);
                                line-height: 1.3;
                            ">${offer.description}</div>
                        </div>
                    </div>

                    <div style="
                        position: absolute;
                        bottom: 5px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 200px;
                        height: 35px;
                        z-index: 10;
                    ">
                        <div style="
                            background: linear-gradient(135deg, #FFD700, #FFA500);
                            color: #8B4513;
                            padding: 8px 16px;
                            border-radius: 20px;
                            font-weight: bold;
                            font-size: 12px;
                            text-align: center;
                            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
                            border: 2px solid #FFD700;
                        ">عرض مميز - ${offer.price}</div>
                    </div>
                </div>
            `).join('');
        }

        // دالة إنشاء قصة جديدة
        function createNewStory() {
            alert('🎬 إنشاء قصة جديدة!\n\nسيتم فتح نافذة إنشاء القصة...\n\n📸 يمكنك إضافة صور وفيديوهات\n📝 كتابة وصف للقصة\n🏷️ إضافة علامات تجارية');
        }

        // دوال التفاعل مع المنشورات
        function likePost(postId) {
            alert('❤️ تم الإعجاب بالمنشور رقم ' + postId);
        }

        function commentPost(postId) {
            alert('💬 إضافة تعليق على المنشور رقم ' + postId);
        }

        function sharePost(postId) {
            alert('📤 مشاركة المنشور رقم ' + postId);
        }

        function openImageModal(imageSrc) {
            alert('🖼️ عرض الصورة بحجم كامل:\n' + imageSrc);
        }

        // دالة تغيير نوع الرسم البياني
        function changeChartType(type) {
            // إزالة التحديد من جميع الأزرار
            document.getElementById('weeklyBtn').style.background = 'transparent';
            document.getElementById('weeklyBtn').style.color = '#666';
            document.getElementById('monthlyBtn').style.background = 'transparent';
            document.getElementById('monthlyBtn').style.color = '#666';
            document.getElementById('yearlyBtn').style.background = 'transparent';
            document.getElementById('yearlyBtn').style.color = '#666';

            // تحديد الزر المختار
            const selectedBtn = document.getElementById(type + 'Btn');
            selectedBtn.style.background = 'linear-gradient(135deg, #16CCC8, #227FCC)';
            selectedBtn.style.color = 'white';

            // تحديث العنوان
            const chartTitle = document.getElementById('chartTitle');
            if (type === 'weekly') {
                chartTitle.textContent = '📊 الأداء الأسبوعي';
            } else if (type === 'monthly') {
                chartTitle.textContent = '📊 الأداء الشهري';
            } else {
                chartTitle.textContent = '📊 الأداء السنوي';
            }

            console.log('تم تغيير نوع الرسم البياني إلى:', type);
        }

        // تحميل البث المباشر
        function loadLiveStreams(serviceName) {
            const container = document.getElementById('liveStreamsContainer');
            const streams = generateLiveStreams(serviceName);

            container.innerHTML = streams.map(stream => `
                <div style="
                    min-width: 75px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    position: relative;
                " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                    <div style="
                        width: 65px;
                        height: 65px;
                        border-radius: 50%;
                        background: linear-gradient(45deg, #8e44ad, #e74c3c, #f39c12, #f1c40f);
                        padding: 3px;
                        margin: 0 auto 8px;
                        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
                        transition: all 0.3s ease;
                        position: relative;
                    ">
                        <div style="
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            background: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            overflow: hidden;
                        ">
                            <img src="${stream.avatar}" alt="${stream.name}" style="
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                                border-radius: 50%;
                            " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="
                                display: none;
                                width: 100%;
                                height: 100%;
                                align-items: center;
                                justify-content: center;
                                background: linear-gradient(45deg, #8e44ad, #e74c3c);
                                color: white;
                                font-size: 18px;
                                border-radius: 50%;
                            ">
                                <i class="${stream.icon}"></i>
                            </div>
                        </div>
                        ${stream.isLive ? `
                            <div style="
                                position: absolute;
                                bottom: 2px;
                                right: 2px;
                                background: #e74c3c;
                                color: white;
                                border-radius: 50%;
                                width: 18px;
                                height: 18px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 8px;
                                font-weight: bold;
                                border: 2px solid white;
                                animation: pulse 1.5s infinite;
                            ">
                                LIVE
                            </div>
                        ` : ''}
                    </div>
                    <div style="
                        font-size: 11px;
                        color: #333;
                        font-weight: 600;
                        line-height: 1.2;
                        max-width: 75px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    ">
                        ${stream.name.length > 8 ? stream.name.substring(0, 8) + '...' : stream.name}
                    </div>
                </div>
            `).join('');
        }

        // بيانات الدول مع الأعلام - الدول العربية والأوروبية
        const countriesData = {
            // الدول العربية
            'العراق': { flag: '🇮🇶', name: 'العراق' },
            'مصر': { flag: '🇪🇬', name: 'مصر' },
            'السعودية': { flag: '🇸🇦', name: 'السعودية' },
            'الإمارات': { flag: '🇦🇪', name: 'الإمارات' },
            'سوريا': { flag: '🇸🇾', name: 'سوريا' },
            'لبنان': { flag: '🇱🇧', name: 'لبنان' },
            'الأردن': { flag: '🇯🇴', name: 'الأردن' },
            'تونس': { flag: '🇹🇳', name: 'تونس' },
            'الجزائر': { flag: '🇩🇿', name: 'الجزائر' },
            'المغرب': { flag: '🇲🇦', name: 'المغرب' },
            'فلسطين': { flag: '🇵🇸', name: 'فلسطين' },
            'ليبيا': { flag: '🇱🇾', name: 'ليبيا' },
            'اليمن': { flag: '🇾🇪', name: 'اليمن' },
            'الكويت': { flag: '🇰🇼', name: 'الكويت' },
            'قطر': { flag: '🇶🇦', name: 'قطر' },
            'البحرين': { flag: '🇧🇭', name: 'البحرين' },
            'عمان': { flag: '🇴🇲', name: 'عمان' },
            'السودان': { flag: '🇸🇩', name: 'السودان' },
            'الصومال': { flag: '🇸🇴', name: 'الصومال' },
            'موريتانيا': { flag: '🇲🇷', name: 'موريتانيا' },
            'جيبوتي': { flag: '🇩🇯', name: 'جيبوتي' },
            'جزر القمر': { flag: '🇰🇲', name: 'جزر القمر' },

            // الدول الأوروبية
            'ألمانيا': { flag: '🇩🇪', name: 'ألمانيا' },
            'فرنسا': { flag: '🇫🇷', name: 'فرنسا' },
            'إيطاليا': { flag: '🇮🇹', name: 'إيطاليا' },
            'إسبانيا': { flag: '🇪🇸', name: 'إسبانيا' },
            'بريطانيا': { flag: '🇬🇧', name: 'بريطانيا' },
            'هولندا': { flag: '🇳🇱', name: 'هولندا' },
            'بلجيكا': { flag: '🇧🇪', name: 'بلجيكا' },
            'سويسرا': { flag: '🇨🇭', name: 'سويسرا' },
            'النمسا': { flag: '🇦🇹', name: 'النمسا' },
            'السويد': { flag: '🇸🇪', name: 'السويد' },
            'النرويج': { flag: '🇳🇴', name: 'النرويج' },
            'الدنمارك': { flag: '🇩🇰', name: 'الدنمارك' },
            'فنلندا': { flag: '🇫🇮', name: 'فنلندا' },
            'البرتغال': { flag: '🇵🇹', name: 'البرتغال' },
            'اليونان': { flag: '🇬🇷', name: 'اليونان' },
            'بولندا': { flag: '🇵🇱', name: 'بولندا' },
            'التشيك': { flag: '🇨🇿', name: 'التشيك' },
            'المجر': { flag: '🇭🇺', name: 'المجر' },
            'رومانيا': { flag: '🇷🇴', name: 'رومانيا' },
            'بلغاريا': { flag: '🇧🇬', name: 'بلغاريا' },
            'كرواتيا': { flag: '🇭🇷', name: 'كرواتيا' },
            'صربيا': { flag: '🇷🇸', name: 'صربيا' },
            'أوكرانيا': { flag: '🇺🇦', name: 'أوكرانيا' },
            'روسيا': { flag: '🇷🇺', name: 'روسيا' },
            'تركيا': { flag: '🇹🇷', name: 'تركيا' },
            'قبرص': { flag: '🇨🇾', name: 'قبرص' },
            'مالطا': { flag: '🇲🇹', name: 'مالطا' },
            'أيسلندا': { flag: '🇮🇸', name: 'أيسلندا' },
            'أيرلندا': { flag: '🇮🇪', name: 'أيرلندا' },
            'لوكسمبورغ': { flag: '🇱🇺', name: 'لوكسمبورغ' },
            'سلوفاكيا': { flag: '🇸🇰', name: 'سلوفاكيا' },
            'سلوفينيا': { flag: '🇸🇮', name: 'سلوفينيا' },
            'إستونيا': { flag: '🇪🇪', name: 'إستونيا' },
            'لاتفيا': { flag: '🇱🇻', name: 'لاتفيا' },
            'ليتوانيا': { flag: '🇱🇹', name: 'ليتوانيا' }
        };

        // تبديل عرض قائمة الدول
        function toggleCountryDropdown() {
            const dropdown = document.getElementById('countryOptions');
            const arrow = document.getElementById('dropdownArrow');

            if (dropdown.style.display === 'none' || dropdown.style.display === '') {
                dropdown.style.display = 'block';
                arrow.style.transform = 'rotate(180deg)';
                loadCountryOptions();
            } else {
                dropdown.style.display = 'none';
                arrow.style.transform = 'rotate(0deg)';
            }
        }

        // تحميل خيارات الدول
        function loadCountryOptions() {
            const container = document.getElementById('countryOptions');
            container.innerHTML = Object.keys(countriesData).map(countryKey => {
                const country = countriesData[countryKey];
                return `
                    <div onclick="selectCountry('${countryKey}')" style="
                        padding: 12px 15px;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        transition: all 0.3s ease;
                        border-bottom: 1px solid #f0f0f0;
                    " onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='white'">
                        <span style="font-size: 18px; width: 25px; text-align: center;">${country.flag}</span>
                        <span style="font-weight: 500; color: #333;">${country.name}</span>
                    </div>
                `;
            }).join('');
        }

        // اختيار دولة
        function selectCountry(countryKey) {
            const country = countriesData[countryKey];

            // تحديث العلم واسم الدولة في المستطيل الرئيسي
            document.getElementById('selectedFlag').textContent = country.flag;
            document.getElementById('selectedCountry').textContent = country.name;

            // إخفاء القائمة
            document.getElementById('countryOptions').style.display = 'none';
            document.getElementById('dropdownArrow').style.transform = 'rotate(0deg)';

            // تحديث بيانات السوق حسب الدولة المختارة
            updateMarketDataByCountry(countryKey);
        }

        // تحديث بيانات السوق حسب الدولة
        function updateMarketDataByCountry(countryKey) {
            // هنا يمكن إضافة منطق تحديث البيانات حسب الدولة
            console.log('تم اختيار الدولة:', countryKey);

            // مثال على تحديث البيانات (يمكن ربطها بـ geoData.js لاحقاً)
            const marketData = getMarketDataForCountry(countryKey);
            updateMarketDisplay(marketData);
        }

        // الحصول على بيانات السوق للدولة
        function getMarketDataForCountry(countryKey) {
            // بيانات تجريبية - يمكن ربطها بـ geoData.js
            const sampleData = {
                'العراق': { growth: '+12%', apartments: '+8%', lands: '+15%', commercial: '+5%' },
                'مصر': { growth: '+10%', apartments: '+6%', lands: '+12%', commercial: '+4%' },
                'السعودية': { growth: '+15%', apartments: '+10%', lands: '+18%', commercial: '+7%' },
                'الإمارات': { growth: '+18%', apartments: '+12%', lands: '+20%', commercial: '+9%' }
            };

            return sampleData[countryKey] || sampleData['العراق'];
        }

        // تحديث عرض بيانات السوق
        function updateMarketDisplay(data) {
            // تحديث النمو الإجمالي
            const growthElement = document.querySelector('#marketAnalysisWidget .fafbfc div[style*="font-size: 28px"]');
            if (growthElement) {
                growthElement.textContent = data.growth;
            }

            // تحديث بيانات الشقق والأراضي والتجاري
            const statsElements = document.querySelectorAll('#marketAnalysisWidget [style*="grid-template-columns"] > div');
            if (statsElements.length >= 3) {
                statsElements[0].querySelector('[style*="font-weight: bold"]').textContent = data.apartments;
                statsElements[1].querySelector('[style*="font-weight: bold"]').textContent = data.lands;
                statsElements[2].querySelector('[style*="font-weight: bold"]').textContent = data.commercial;
            }
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const dropdown = document.querySelector('.country-selector');
            if (!dropdown.contains(event.target)) {
                document.getElementById('countryOptions').style.display = 'none';
                document.getElementById('dropdownArrow').style.transform = 'rotate(0deg)';
            }
        });

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل قصص البائعين للعقارات
            loadSellersStories('عقارات');

            // تحميل البث المباشر للعقارات
            loadLiveStreams('عقارات');

            // تحميل العروض المميزة للعقارات
            loadPremiumOffers('عقارات');

            // تحميل منشورات البائعين للعقارات
            loadSellersPosts('عقارات');

            // تحديث حالة الأسهم عند التحميل
            setTimeout(() => {
                updateArrowsState();
                updateLiveArrowsState();
                updatePremiumArrowsState();
            }, 100);

            // تحديث الأسهم عند التمرير اليدوي
            const storiesContainer = document.getElementById('sellersStoriesContainer');
            const liveContainer = document.getElementById('liveStreamsContainer');
            const premiumContainer = document.getElementById('premiumOffersContainer');

            storiesContainer.addEventListener('scroll', updateArrowsState);
            liveContainer.addEventListener('scroll', updateLiveArrowsState);
            premiumContainer.addEventListener('scroll', updatePremiumArrowsState);

            console.log('تم تحميل شريط القصص بنجاح! 🎉');
            console.log('تم تحميل شريط البث المباشر بنجاح! 🔴');
            console.log('تم تحميل العروض المميزة بنجاح! ⭐');
            console.log('تم تحميل منشورات البائعين بنجاح! 📰');
            console.log('عدد القصص المحملة:', generateSellersStories('عقارات').length);
            console.log('عدد البث المباشر:', generateLiveStreams('عقارات').length);
            console.log('عدد العروض المميزة:', generatePremiumOffers('عقارات').length);
            console.log('عدد المنشورات:', generateSellersPosts('عقارات').length);
        });
    </script>
</body>
</html>
