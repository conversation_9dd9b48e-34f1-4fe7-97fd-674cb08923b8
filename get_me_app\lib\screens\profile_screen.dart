import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
import '../models/user.dart';
import '../models/post.dart';
import '../widgets/post_card.dart';

class ProfileScreen extends StatefulWidget {
  final User user;

  const ProfileScreen({
    super.key,
    required this.user,
  });

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late List<Post> _userPosts;
  final TextEditingController _companyInfoController = TextEditingController();
  final TextEditingController _certificatesController = TextEditingController();
  String _companyInfo = '';
  String _certificates = '';
  List<String> _photoAlbums = ['الألبوم الرئيسي', 'منتجاتنا', 'فريق العمل'];
  List<String> _videoAlbums = ['فيديوهات تعريفية', 'شهادات العملاء'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _userPosts = Post.getDemoPosts()
        .where((post) => post.author.id == widget.user.id)
        .toList();
    _companyInfo = 'شركة رائدة في مجال التكنولوجيا والحلول الرقمية';
    _certificates = 'شهادة ISO 9001\nشهادة الجودة الدولية\nعضوية غرفة التجارة';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              elevation: 1,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black87),
                onPressed: () => Navigator.pop(context),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.more_vert, color: Colors.black87),
                  onPressed: () => _showProfileOptions(),
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: _buildProfileHeader(),
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Tab Bar
            Container(
              color: Colors.white,
              child: TabBar(
                controller: _tabController,
                labelColor: const Color(0xFF2196F3),
                unselectedLabelColor: Colors.grey[600],
                indicatorColor: const Color(0xFF2196F3),
                labelStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                tabs: const [
                  Tab(text: 'المنشورات'),
                  Tab(text: 'الصور'),
                  Tab(text: 'الفيديوهات'),
                  Tab(text: 'المعلومات'),
                ],
              ),
            ),

            // Tab Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPostsTab(),
                  _buildPhotosTab(),
                  _buildVideosTab(),
                  _buildInfoTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 40),
          // Profile Picture - أكبر حجماً
          Stack(
            children: [
              CircleAvatar(
                radius: 80,
                backgroundColor: const Color(0xFF2196F3),
                child: CircleAvatar(
                  radius: 77,
                  backgroundImage: widget.user.profileImage.isNotEmpty
                      ? NetworkImage(widget.user.profileImage)
                      : null,
                  child: widget.user.profileImage.isEmpty
                      ? const Icon(Icons.person, size: 80, color: Colors.grey)
                      : null,
                ),
              ),
              if (widget.user.isPremium)
                Positioned(
                  bottom: 5,
                  right: 5,
                  child: Container(
                    width: 35,
                    height: 35,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.verified,
                      color: Color(0xFF2196F3),
                      size: 25,
                    ),
                  ),
                ),
              // زر تعديل الصورة
              Positioned(
                bottom: 5,
                left: 5,
                child: GestureDetector(
                  onTap: _changeProfilePicture,
                  child: Container(
                    width: 35,
                    height: 35,
                    decoration: const BoxDecoration(
                      color: Color(0xFF2196F3),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Name and Type
          Text(
            widget.user.name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),

          const SizedBox(height: 8),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: const Color(0xFF2196F3).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getUserTypeDisplayName(),
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF2196F3),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Stats
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('المنشورات', widget.user.postsCount),
              _buildStatItem('المتابعون', widget.user.followersCount),
              _buildStatItem('المتابَعون', widget.user.followingCount),
            ],
          ),

          const SizedBox(height: 20),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _editProfile,
                  icon: const Icon(Icons.edit, size: 18),
                  label: const Text(
                    'تعديل الملف الشخصي',
                    style: TextStyle(fontSize: 14),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2196F3),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: _shareProfile,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[200],
                  foregroundColor: Colors.black87,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Icon(Icons.share, size: 18),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2196F3),
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildPostsTab() {
    if (_userPosts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.post_add,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد منشورات بعد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'ابدأ بمشاركة أول منشور لك',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: _userPosts.length,
      itemBuilder: (context, index) {
        return Column(
          children: [
            PostCard(
              post: _userPosts[index],
              currentUser: widget.user,
              onLike: () {},
              onComment: () {},
              onShare: () {},
            ),
            const SizedBox(height: 8),
          ],
        );
      },
    );
  }

  Widget _buildPhotosTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // زر إضافة ألبوم جديد
          ElevatedButton.icon(
            onPressed: _addPhotoAlbum,
            icon: const Icon(Icons.add),
            label: const Text('إضافة ألبوم صور'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2196F3),
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(height: 16),

          // قائمة الألبومات
          Expanded(
            child: ListView.builder(
              itemCount: _photoAlbums.length,
              itemBuilder: (context, index) {
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: const Color(0xFF2196F3).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.photo_library,
                        color: Color(0xFF2196F3),
                      ),
                    ),
                    title: Text(
                      _photoAlbums[index],
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text('${(index + 1) * 5} صورة'),
                    trailing: PopupMenuButton(
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'add',
                          child: Text('إضافة صور'),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Text('تعديل الاسم'),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('حذف الألبوم'),
                        ),
                      ],
                      onSelected: (value) =>
                          _handleAlbumAction(value, index, 'photo'),
                    ),
                    onTap: () => _openPhotoAlbum(_photoAlbums[index]),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideosTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // زر إضافة ألبوم فيديو جديد
          ElevatedButton.icon(
            onPressed: _addVideoAlbum,
            icon: const Icon(Icons.add),
            label: const Text('إضافة ألبوم فيديوهات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2196F3),
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(height: 16),

          // قائمة ألبومات الفيديو
          Expanded(
            child: ListView.builder(
              itemCount: _videoAlbums.length,
              itemBuilder: (context, index) {
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.video_library,
                        color: Colors.red,
                      ),
                    ),
                    title: Text(
                      _videoAlbums[index],
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text('${(index + 1) * 3} فيديو'),
                    trailing: PopupMenuButton(
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'add',
                          child: Text('إضافة فيديوهات'),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Text('تعديل الاسم'),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('حذف الألبوم'),
                        ),
                      ],
                      onSelected: (value) =>
                          _handleAlbumAction(value, index, 'video'),
                    ),
                    onTap: () => _openVideoAlbum(_videoAlbums[index]),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الشركة
            _buildEditableInfoSection(
              'معلومات الشركة',
              _companyInfo,
              _companyInfoController,
              () => _editCompanyInfo(),
            ),
            const SizedBox(height: 20),

            // الشهادات
            _buildEditableInfoSection(
              'الشهادات والمؤهلات',
              _certificates,
              _certificatesController,
              () => _editCertificates(),
            ),
            const SizedBox(height: 20),

            // معلومات أساسية
            _buildInfoSection('نبذة شخصية', widget.user.bio),
            const SizedBox(height: 20),
            _buildInfoSection('البريد الإلكتروني', widget.user.email),
            const SizedBox(height: 20),
            _buildInfoSection('تاريخ الانضمام',
                '${widget.user.joinDate.day}/${widget.user.joinDate.month}/${widget.user.joinDate.year}'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          content.isEmpty ? 'غير محدد' : content,
          style: TextStyle(
            fontSize: 14,
            color: content.isEmpty ? Colors.grey : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildEditableInfoSection(String title, String content,
      TextEditingController controller, VoidCallback onEdit) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: onEdit,
                  icon: const Icon(Icons.edit, size: 20),
                  color: const Color(0xFF2196F3),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              content.isEmpty ? 'اضغط لإضافة معلومات' : content,
              style: TextStyle(
                fontSize: 14,
                color: content.isEmpty ? Colors.grey : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showProfileOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('الإعدادات'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to settings
              },
            ),
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('المساعدة'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to help
              },
            ),
            ListTile(
              leading: const Icon(Icons.logout, color: Colors.red),
              title: const Text(
                'تسجيل الخروج',
                style: TextStyle(color: Colors.red),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Logout
              },
            ),
          ],
        ),
      ),
    );
  }

  // دوال إضافية مطلوبة
  String _getUserTypeDisplayName() {
    switch (widget.user.userType) {
      case UserType.guest:
        return 'زائر';
      case UserType.buyer:
        return 'مشتري';
      case UserType.seller:
        return 'بائع';
      case UserType.promoter:
        return 'مروج';
      case UserType.teacher:
        return 'مدرس';
      default:
        return 'مستخدم';
    }
  }

  void _changeProfilePicture() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير صورة الملف الشخصي'),
        content: const Text('سيتم إضافة هذه الميزة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _editProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الملف الشخصي'),
        content: const Text('سيتم إضافة هذه الميزة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _shareProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم نسخ رابط الملف الشخصي')),
    );
  }

  void _editCompanyInfo() {
    _companyInfoController.text = _companyInfo;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل معلومات الشركة'),
        content: TextField(
          controller: _companyInfoController,
          maxLines: 5,
          decoration: const InputDecoration(
            hintText: 'اكتب معلومات عن شركتك...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _companyInfo = _companyInfoController.text;
              });
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _editCertificates() {
    _certificatesController.text = _certificates;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الشهادات والمؤهلات'),
        content: TextField(
          controller: _certificatesController,
          maxLines: 5,
          decoration: const InputDecoration(
            hintText: 'اكتب شهاداتك ومؤهلاتك...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _certificates = _certificatesController.text;
              });
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _addPhotoAlbum() {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة ألبوم صور جديد'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'اسم الألبوم',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                setState(() {
                  _photoAlbums.add(controller.text);
                });
              }
              Navigator.pop(context);
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _addVideoAlbum() {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة ألبوم فيديوهات جديد'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'اسم الألبوم',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                setState(() {
                  _videoAlbums.add(controller.text);
                });
              }
              Navigator.pop(context);
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _handleAlbumAction(String action, int index, String type) {
    switch (action) {
      case 'add':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text('إضافة ${type == 'photo' ? 'صور' : 'فيديوهات'} جديدة')),
        );
        break;
      case 'edit':
        _editAlbumName(index, type);
        break;
      case 'delete':
        _deleteAlbum(index, type);
        break;
    }
  }

  void _editAlbumName(int index, String type) {
    final controller = TextEditingController();
    final albums = type == 'photo' ? _photoAlbums : _videoAlbums;
    controller.text = albums[index];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل اسم الألبوم'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'اسم الألبوم الجديد',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                setState(() {
                  albums[index] = controller.text;
                });
              }
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _deleteAlbum(int index, String type) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الألبوم'),
        content: const Text('هل أنت متأكد من حذف هذا الألبوم؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                if (type == 'photo') {
                  _photoAlbums.removeAt(index);
                } else {
                  _videoAlbums.removeAt(index);
                }
              });
              Navigator.pop(context);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _openPhotoAlbum(String albumName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('فتح ألبوم الصور: $albumName')),
    );
  }

  void _openVideoAlbum(String albumName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('فتح ألبوم الفيديوهات: $albumName')),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _companyInfoController.dispose();
    _certificatesController.dispose();
    super.dispose();
  }
}
