import 'package:flutter/material.dart';
import 'package:flutter_login/flutter_login.dart';
import 'package:animations/animations.dart';
import '../models/user.dart';
import 'main_screen.dart';
import 'subscription_plans_screen.dart';

class LoginFlowScreen extends StatefulWidget {
  const LoginFlowScreen({super.key});

  @override
  State<LoginFlowScreen> createState() => _LoginFlowScreenState();
}

class _LoginFlowScreenState extends State<LoginFlowScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-1, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Duration get loginTime => const Duration(milliseconds: 2250);

  Future<String?> _authUser(LoginData data) async {
    debugPrint('Name: ${data.name}, Password: ${data.password}');

    await Future.delayed(loginTime);

    if (data.name == 'admin' && data.password == '123456') {
      return null;
    }

    return 'اسم المستخدم أو كلمة المرور غير صحيحة';
  }

  Future<String?> _signupUser(SignupData data) async {
    debugPrint('Signup Name: ${data.name}, Password: ${data.password}');

    await Future.delayed(loginTime);

    return null;
  }

  Future<String?> _recoverPassword(String name) async {
    debugPrint('Name: $name');

    await Future.delayed(loginTime);

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('login/login2.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
              colors: [
                Colors.black.withOpacity(0.1),
                Colors.black.withOpacity(0.7),
                Colors.black.withOpacity(0.9),
              ],
            ),
          ),
          child: SafeArea(
            child: Row(
              children: [
                // Login Panel (Left Side)
                Expanded(
                  flex: 5,
                  child: _buildLoginPanel(),
                ),
                // Image Space (Right Side)
                const Expanded(
                  flex: 3,
                  child: SizedBox(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginPanel() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.95),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 30,
                offset: const Offset(0, 15),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildLoginForm(),
              ),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF667eea).withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Image.asset(
              'login/logo.png',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // Fallback if image doesn't load
                return Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.shopping_bag_rounded,
                    color: Colors.white,
                    size: 40,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 24),

        // Title
        const Text(
          'Get Me',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1C1E21),
          ),
        ),
        const SizedBox(height: 8),

        Text(
          'سجل دخولك للمتابعة',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildLoginForm() {
    return FlutterLogin(
      title: '',
      logo: null,
      onLogin: _authUser,
      onSignup: _signupUser,
      onSubmitAnimationCompleted: () {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => MainScreen(currentUser: User.demo()),
          ),
        );
      },
      onRecoverPassword: _recoverPassword,
      theme: LoginTheme(
        primaryColor: const Color(0xFF667eea),
        accentColor: const Color(0xFF764ba2),
        errorColor: Colors.red[400]!,
        titleStyle: const TextStyle(
          color: Colors.transparent,
          fontSize: 0,
        ),
        bodyStyle: const TextStyle(
          fontFamily: 'Cairo',
          color: Color(0xFF1C1E21),
        ),
        textFieldStyle: const TextStyle(
          fontFamily: 'Cairo',
          color: Color(0xFF1C1E21),
        ),
        buttonStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
        cardTheme: CardTheme(
          color: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(0),
          ),
        ),
        inputTheme: InputDecorationTheme(
          filled: true,
          fillColor: const Color(0xFFF5F6F7),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF667eea),
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: Colors.red[400]!,
              width: 2,
            ),
          ),
          labelStyle: TextStyle(
            color: Colors.grey[600],
            fontFamily: 'Cairo',
          ),
          hintStyle: TextStyle(
            color: Colors.grey[500],
            fontFamily: 'Cairo',
          ),
        ),
        buttonTheme: LoginButtonTheme(
          splashColor: const Color(0xFF667eea).withOpacity(0.2),
          backgroundColor: const Color(0xFF667eea),
          highlightColor: const Color(0xFF764ba2),
          elevation: 8,
          highlightElevation: 12,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      userValidator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال اسم المستخدم';
        }
        return null;
      },
      passwordValidator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
      messages: LoginMessages(
        userHint: 'اسم المستخدم',
        passwordHint: 'كلمة المرور',
        confirmPasswordHint: 'تأكيد كلمة المرور',
        loginButton: 'تسجيل الدخول',
        signupButton: 'إنشاء حساب جديد',
        forgotPasswordButton: 'نسيت كلمة المرور؟',
        recoverPasswordButton: 'استعادة',
        goBackButton: 'رجوع',
        confirmPasswordError: 'كلمات المرور غير متطابقة',
        recoverPasswordDescription: 'سنرسل لك رابط استعادة كلمة المرور',
        recoverPasswordSuccess: 'تم إرسال رابط الاستعادة بنجاح',
      ),
      userType: LoginUserType.name,
      initialAuthMode: AuthMode.login,
      loginAfterSignUp: false,
      hideForgotPasswordButton: false,
      scrollable: true,
    );
  }

  Widget _buildFooter() {
    return Column(
      children: [
        // Demo Credentials
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF667eea).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF667eea).withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Text(
                'بيانات تجريبية',
                style: TextStyle(
                  color: const Color(0xFF667eea),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'اسم المستخدم: admin\nكلمة المرور: 123456',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 12,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),

        // Subscription Plans Button
        TextButton(
          onPressed: _navigateToSubscriptionPlans,
          child: Text(
            'عرض خطط الاشتراك',
            style: TextStyle(
              color: const Color(0xFF667eea),
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToSubscriptionPlans() {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const SubscriptionPlansScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SharedAxisTransition(
            animation: animation,
            secondaryAnimation: secondaryAnimation,
            transitionType: SharedAxisTransitionType.vertical,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }
}
