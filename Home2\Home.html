<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Me App</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .services-section {
            margin: 140px 0 0 0;
            padding: 15px 0 0 0;
            background: none;
        }
        .services-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }
        .services-wrapper {
            display: flex;
            flex-direction: row;
            gap: 20px;
            transition: transform 1s ease;
            width: fit-content;
        }
        .service-card {
            width: 180px;
            height: 260px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            transition: transform 0.3s;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }
        .service-card:hover {
            transform: translateY(-5px);
        }
        .service-image {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .service-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .service-image-placeholder {
            font-size: 40px;
            color: #666;
        }
        .service-name {
            height: 60px;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 15px;
            font-weight: bold;
            text-align: center;
        }
        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            z-index: 10;
        }
        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .nav-arrow.prev {
            right: 10px;
        }
        .nav-arrow.next {
            left: 10px;
        }
        /* شريط العروض المميزة */
        .premium-offers-section {
            margin-bottom: 30px;
            position: relative;
        }
        .premium-offers-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
        }
        .premium-offers-wrapper {
            display: flex;
            gap: 20px;
            transition: transform 1s ease;
            width: fit-content;
        }
        .premium-offer-card {
            width: 380px;
            height: 200px;
            border-radius: 15px;
            border: 3px solid #FFD700;
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            flex-shrink: 0;
            overflow: hidden;
        }
        .premium-offer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        }
        .premium-offer-content {
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 2;
            overflow: hidden;
            border-radius: 12px;
        }
        .premium-offer-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        }
        .premium-offer-image-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #16CCC8, #227FCC);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            border-radius: 12px;
        }
        .premium-offer-overlay {
            position: absolute;
            top: 0;
            right: 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.5), rgba(0,0,0,0.3));
            color: white;
            padding: 15px;
            z-index: 3;
            border-radius: 0 12px 0 15px;
            max-width: 70%;
        }
        .premium-offer-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.9);
            line-height: 1.2;
        }
        .premium-offer-subtitle {
            font-size: 11px;
            opacity: 0.95;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.9);
            line-height: 1.3;
        }
        .premium-offer-footer {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 35px;
            z-index: 10;
        }
        .premium-offer-footer::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 23px;
            z-index: -1;
        }
        .premium-badge {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            box-shadow:
                0 4px 15px rgba(255, 165, 0, 0.4),
                0 0 20px rgba(255, 255, 0, 0.6),
                0 0 40px rgba(255, 255, 0, 0.3);
            animation: badge-glow 2s ease-in-out infinite alternate;
        }
        .premium-nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            border: none;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            z-index: 10;
            box-shadow: 0 4px 15px rgba(255, 165, 0, 0.4);
        }
        .premium-nav-arrow:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(255, 165, 0, 0.6);
        }
        .premium-nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .premium-nav-arrow.prev {
            right: 10px;
        }
        .premium-nav-arrow.next {
            left: 10px;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html, body { overflow-x: hidden; max-width: 100%; }
        body { font-family: 'Cairo', sans-serif; background: #f5f5f5; direction: rtl; color: #333; }
        /* ...جميع أكواد CSS المنسوخة من get_me_updated.html حتى نهاية أنماط الرسائل... */
        /* الشريط العلوي */
        .header { background: linear-gradient(90deg, #16CCC8, #227FCC); position: fixed !important; top: 0 !important; left: 0 !important; right: 0 !important; z-index: 1000 !important; height: 70px; display: flex; align-items: center; justify-content: space-between; padding: 0 20px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); width: 100% !important; }
        .app-name { font-size: 40px; font-weight: bold; color: white; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .search-section { display: flex; align-items: center; gap: 15px; flex: 1; max-width: 600px; margin: 0 30px; }
        .search-input { width: 100%; height: 40px; padding: 12px 20px; border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 25px; background: rgba(255, 255, 255, 0.15); font-family: 'Cairo', sans-serif; font-size: 16px; outline: none; color: white; transition: all 0.3s ease; font-weight: 500; backdrop-filter: blur(10px); }
        .search-input::placeholder { color: rgba(255,255,255,0.7); font-weight: 500; }
        .search-btn { background: none; border: none; cursor: pointer; color: white; font-size: 24px; padding: 10px; }
        .user-section { display: flex; align-items: center; gap: 15px; }
        .menu-icon { color: white; font-size: 20px; cursor: pointer; padding: 8px; position: relative; }
        .main-menu-dropdown { position: absolute; top: 100%; right: 0; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border: 1px solid rgba(0, 0, 0, 0.1); border-radius: 12px; min-width: 250px; max-height: 400px; overflow-y: auto; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15); z-index: 1000; opacity: 0; visibility: hidden; transform: translateX(20px); transition: all 0.3s ease; margin-top: 10px; }
        .main-menu-dropdown.show { opacity: 1; visibility: visible; transform: translateX(0); }
        .menu-item { display: flex; align-items: center; gap: 12px; padding: 12px 16px; color: #4a4a4a; cursor: pointer; transition: color 0.3s ease; font-family: 'Cairo', sans-serif; font-size: 14px; }
        .menu-item:hover { color: #667eea; }
        .menu-item i { font-size: 16px; width: 20px; text-align: center; transition: color 0.3s ease; }
        .menu-item:hover i { color: #667eea; }
        .menu-item.logout { color: #e74c3c; }
        .menu-item.logout:hover { color: #c0392b; }
        .menu-item.logout:hover i { color: #c0392b; }
        .menu-divider { height: 1px; background: rgba(0, 0, 0, 0.1); margin: 8px 0; }
        .user-avatar { width: 50px; height: 50px; border-radius: 50%; border: 3px solid #28a745; box-shadow: 0 0 15px rgba(40, 167, 69, 0.6); }
        .user-avatar img { width: 100%; height: 100%; border-radius: 50%; object-fit: cover; }
        .user-name { color: white; font-size: 16px; font-weight: 600; }
        .nav-bar { background: white; position: fixed !important; top: 70px !important; left: 0 !important; right: 0 !important; z-index: 999 !important; height: 60px; display: flex; align-items: center; padding: 0 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); width: 100% !important; }
        .nav-items { display: flex; align-items: center; width: 100%; gap: 25px; }
        .nav-item { display: flex; align-items: center; gap: 8px; padding: 12px 18px; cursor: pointer; transition: all 0.3s; color: #495057; font-weight: 500; position: relative; font-size: 14px; }
        .nav-item:hover, .nav-item.active { color: #16CCC8; transform: translateY(-2px); }
        .nav-item:hover i { transform: scale(1.1); color: #16CCC8; }
        .nav-item i { font-size: 24px; transition: all 0.3s ease; }
        .nav-item.home-item { margin-left: 80px; }
        .nav-item.home-item i { background: linear-gradient(135deg, #16CCC8, #227FCC); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
        .nav-item.home-item:hover i, .nav-item.home-item.active i { background: linear-gradient(135deg, #16CCC8, #227FCC); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
        .nav-item.location-item { margin-right: auto; background: rgba(22, 204, 200, 0.1); border-radius: 20px; border: 1px solid rgba(22, 204, 200, 0.3); }
        .nav-item.location-item:hover { background: rgba(22, 204, 200, 0.2); border-color: rgba(22, 204, 200, 0.5); }
        .nav-item.has-notification i { color: #ff0000 !important; -webkit-text-fill-color: #ff0000 !important; }
        .notification-badge { position: absolute; top: -5px; left: -5px; background: #ff0000; color: white; border-radius: 50%; width: 18px; height: 18px; display: flex; align-items: center; justify-content: center; font-size: 11px; font-weight: bold; }
        .main-content { margin-top: 130px; padding: 20px; }
        /* ...باقي أكواد CSS حتى نهاية أنماط الرسائل... */
    </style>
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="header">
        <div class="user-section">
            <div class="menu-icon" onclick="toggleMainMenu()">
                <i class="fas fa-bars"></i>
                <!-- القائمة المنسدلة -->
                <div class="main-menu-dropdown" id="mainMenuDropdown">
                    <div class="menu-item" onclick="toggleDarkMode()">
                        <i class="fas fa-moon"></i>
                        <span>الوضع الليلي</span>
                    </div>
                    <div class="menu-item" onclick="toggleLightMode()">
                        <i class="fas fa-sun"></i>
                        <span>الوضع النهاري</span>
                    </div>
                    <div class="menu-item" onclick="openDarkModeCustomization()">
                        <i class="fas fa-palette"></i>
                        <span>تخصيص الوضع الليلي</span>
                    </div>
                    <div class="menu-item" onclick="openNotificationSettings()">
                        <i class="fas fa-bell"></i>
                        <span>إعدادات الإشعارات</span>
                    </div>
                    <div class="menu-item" onclick="changeLanguage()">
                        <i class="fas fa-globe"></i>
                        <span>تغيير اللغة</span>
                    </div>
                    <div class="menu-item" onclick="openPrivacySettings()">
                        <i class="fas fa-shield-alt"></i>
                        <span>الخصوصية والأمان</span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item" onclick="viewStatistics()">
                        <i class="fas fa-chart-bar"></i>
                        <span>إحصائيات الملف الشخصي</span>
                    </div>
                    <div class="menu-item" onclick="viewEarnings()">
                        <i class="fas fa-dollar-sign"></i>
                        <span>تقرير الأرباح</span>
                    </div>
                    <div class="menu-item" onclick="viewTransactions()">
                        <i class="fas fa-receipt"></i>
                        <span>سجل المعاملات</span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item" onclick="upgradeToPremium()">
                        <i class="fas fa-crown"></i>
                        <span>الحساب المميز</span>
                    </div>
                    <div class="menu-item" onclick="customizeProfile()">
                        <i class="fas fa-palette"></i>
                        <span>تخصيص الملف الشخصي</span>
                    </div>
                    <div class="menu-item" onclick="syncData()">
                        <i class="fas fa-sync"></i>
                        <span>مزامنة البيانات</span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item" onclick="openHelpCenter()">
                        <i class="fas fa-question-circle"></i>
                        <span>مركز المساعدة</span>
                    </div>
                    <div class="menu-item" onclick="contactSupport()">
                        <i class="fas fa-headset"></i>
                        <span>اتصل بنا</span>
                    </div>
                    <div class="menu-item" onclick="viewTerms()">
                        <i class="fas fa-file-contract"></i>
                        <span>الشروط والأحكام</span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item logout" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </div>
                </div>
            </div>
            <div class="user-avatar" onclick="openMyProfile()" style="cursor: pointer;">
                <img src="images/حسين نهاد.png" alt="حسين نهاد">
            </div>
            <div class="user-name" onclick="openMyProfile()" style="cursor: pointer;">Hussein Nihad</div>
        </div>

        <div class="search-section">
            <input type="text" class="search-input" placeholder="اختر نوع الخدمة">
            <button class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>

        <div class="app-name" onclick="showFeed()" style="cursor: pointer;">Get Me</div>
    </div>

    <!-- شريط التنقل -->
    <div class="nav-bar">
        <div class="nav-items">
            <div class="nav-item home-item active" onclick="showFeed()">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </div>
            <div class="nav-item has-notification" onclick="showNotifications()">
                <i class="fas fa-bell"></i>
                <span>الإشعارات</span>
                <div class="notification-badge">5</div>
            </div>
            <div class="nav-item has-notification" onclick="showMessages()">
                <i class="fas fa-envelope"></i>
                <span>الرسائل</span>
                <div class="notification-badge">3</div>
            </div>
            <div class="nav-item" onclick="showMyProfile()">
                <i class="fas fa-user"></i>
                <span>الملف الشخصي</span>
            </div>
            <div class="nav-item location-item" onclick="showLocationSelector()">
                <i class="fas fa-map-marker-alt"></i>
                <span id="currentLocation">العراق</span>
                <i class="fas fa-chevron-down" style="font-size: 12px; margin-right: 5px;"></i>
            </div>
        </div>
    </div>

    <!-- شريط الخدمات -->
    <div class="services-section">
        <div class="services-container">
            <button class="nav-arrow prev" id="prevBtn">
                <i class="fas fa-chevron-right"></i>
            </button>
            <button class="nav-arrow next" id="nextBtn">
                <i class="fas fa-chevron-left"></i>
            </button>
            <div class="services-wrapper" id="servicesWrapper">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <!-- شريط العرض المميز -->
    <div class="premium-offers-section">
        <div class="premium-offers-container">
            <button class="premium-nav-arrow prev" id="premiumPrevBtn">
                <i class="fas fa-chevron-right"></i>
            </button>
            <button class="premium-nav-arrow next" id="premiumNextBtn">
                <i class="fas fa-chevron-left"></i>
            </button>
            <div class="premium-offers-wrapper" id="premiumOffersWrapper">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>

    <!-- شريط شغلني شاهد واربح -->
    <div class="watch-earn-section">
        <div class="watch-earn-title">
            <i class="fas fa-briefcase"></i>
            شغلني - شاهد واربح
        </div>
        <div class="watch-earn-bar">
            <div class="watch-earn-item">
                <video class="watch-earn-img" width="160" height="90" controls>
                    <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                    متصفحك لا يدعم تشغيل الفيديو.
                </video>
                <div class="watch-earn-info">
                    <div class="watch-earn-ad-title">إعلان مطعم جديد</div>
                    <div class="watch-earn-ad-desc">شاهد الإعلان واربح نقاطًا إضافية!</div>
                </div>
                <button class="watch-earn-btn">شاهد الآن</button>
            </div>
            <div class="watch-earn-item">
                <video class="watch-earn-img" width="160" height="90" controls>
                    <source src="https://www.w3schools.com/html/movie.mp4" type="video/mp4">
                    متصفحك لا يدعم تشغيل الفيديو.
                </video>
                <div class="watch-earn-info">
                    <div class="watch-earn-ad-title">إعلان منتج مميز</div>
                    <div class="watch-earn-ad-desc">شاهد الإعلان واحصل على مكافأة!</div>
                </div>
                <button class="watch-earn-btn">شاهد الآن</button>
            </div>
            <!-- يمكنك تكرار العناصر حسب الحاجة -->
        </div>
    </div>

    <script>
        // متغير اللغة الحالية (ar أو en)
        let appLang = 'ar'; // يمكنك تغييره إلى 'en' عند تغيير اللغة

        // مصفوفة الخدمات مع اسم عربي (اسم الصورة) وإنجليزي
const services = [
    { nameAr: 'صناعة', nameEn: 'Industry', icon: 'fas fa-industry', img: 'Industry.jpg' },
    { nameAr: 'طبية', nameEn: 'Doctor', icon: 'fas fa-pills', img: 'Doctor.jpg' },
    { nameAr: 'زراعية', nameEn: 'Agriculture', icon: 'fas fa-seedling', img: 'Agriculture.jpg' },
    { nameAr: 'عقارية', nameEn: 'Eqarat', icon: 'fas fa-home', img: 'Eqarat.jpg' },
    { nameAr: 'سيارات', nameEn: 'Car', icon: 'fas fa-car', img: 'Car.jpg' },
    { nameAr: 'تجميل', nameEn: 'Nature Center', icon: 'fas fa-spa', img: 'Cosmetics.jpg' },
    { nameAr: 'مطاعم', nameEn: 'Restaurant', icon: 'fas fa-utensils', img: 'Restaurant.jpg' },
    { nameAr: 'كافيهات', nameEn: 'Cafe', icon: 'fas fa-coffee', img: 'Cafe.jpg' },
    { nameAr: 'مصارف', nameEn: 'Banks', icon: 'fas fa-coins', img: 'Banks.jpg' },
    { nameAr: 'حواله', nameEn: 'Transfer', icon: 'fas fa-exchange-alt', img: 'Exchange.jpg' },
    { nameAr: 'توصيل', nameEn: 'Delivery', icon: 'fas fa-truck', img: 'Delivery.jpg' },
    { nameAr: 'استيراد وتصدير', nameEn: 'ImportExport', icon: 'fas fa-ship', img: 'ImportExport.jpg' },
    { nameAr: 'قرطاسية', nameEn: 'Stationery', icon: 'fas fa-pen', img: 'Stationery.jpg' },
    { nameAr: 'محلات', nameEn: 'Mahalat', icon: 'fas fa-tshirt', img: 'Mahalat.jpg' },
    { nameAr: 'سياحة', nameEn: 'Tourism', icon: 'fas fa-plane', img: 'Tourism.jpg' },
    { nameAr: 'سفر', nameEn: 'Travel', icon: 'fas fa-plane-departure', img: 'Travel.jpg' },
    { nameAr: 'سوبرماركيت', nameEn: 'Supermarket', icon: 'fas fa-store', img: 'Supermarket.jpg' },
    { nameAr: 'الكترونيات', nameEn: 'Electronics', icon: 'fas fa-tv', img: 'Electronics.jpg' },
    { nameAr: 'مكتبات', nameEn: 'Library', icon: 'fas fa-book', img: 'Stationery.jpg' }, // لا يوجد مكتبات، استخدم قرطاسية
    { nameAr: 'خدمات أخرى', nameEn: 'Other Services', icon: 'fas fa-concierge-bell', img: 'Supermarket.jpg' } // استخدم صورة عامة
];

// عند الاستخدام اربط المسار الكامل
// لا حاجة لإضافة مسار هنا، سيتم معالجته في getServiceImage


        function getServiceImage(service) {
            return `images/Types of services/${service.img}`;
        }

        // بيانات العروض المميزة (O1.jpg إلى O22.jpg)
        const premiumOffers = Array.from({length: 22}, (_, i) => ({
            id: i + 1,
            title: `عرض مميز ${i + 1}`,
            subtitle: `تفاصيل العرض ${i + 1}`,
            image: `O${i + 1}.jpg`,
            icon: 'fas fa-star'
        }));

        // متغيرات التمرير
        let currentPosition = 0;
        const visibleCards = 5;
        const moveDistance = 195; // عرض البطاقة + الفجوة

        // ملء الخدمات
        function populateServices() {
            const wrapper = document.getElementById('servicesWrapper');
            wrapper.innerHTML = services.map(service =>
                '<div class="service-card" onclick="openServicePage(\'' + service.nameAr + '\')">' +
                    '<div class="service-image">' +
                        '<img src="' + getServiceImage(service) + '" alt="' + service.nameAr + '" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">' +
                        '<div class="service-image-placeholder" style="display: none;">' +
                            '<i class="' + service.icon + '"></i>' +
                        '</div>' +
                    '</div>' +
                    '<div class="service-name">' + (appLang === 'ar' ? service.nameAr : service.nameEn) + '</div>' +
                '</div>'
            ).join('');
            updateNavigationButtons();
        }

        // تحديث أزرار التنقل للخدمات
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const maxPosition = (services.length - visibleCards) * moveDistance;
            prevBtn.disabled = currentPosition <= 0;
            nextBtn.disabled = currentPosition >= maxPosition;
        }

        // تحريك الخدمات
        function moveServices(direction) {
            const wrapper = document.getElementById('servicesWrapper');
            const maxPosition = (services.length - visibleCards) * moveDistance;
            if (direction === 'right' && currentPosition < maxPosition) {
                currentPosition += moveDistance;
            } else if (direction === 'left' && currentPosition > 0) {
                currentPosition -= moveDistance;
            }
            currentPosition = Math.max(0, Math.min(currentPosition, maxPosition));
            wrapper.style.transform = 'translateX(-' + currentPosition + 'px)';
            updateNavigationButtons();
        }

        document.getElementById('prevBtn').addEventListener('click', function() { moveServices('left'); });
        document.getElementById('nextBtn').addEventListener('click', function() { moveServices('right'); });
        document.addEventListener('DOMContentLoaded', function() {
            populateServices();
        });

        // متغيرات التمرير
        let premiumCurrentPosition = 0;
        const premiumVisibleCards = 2;
        const premiumMoveDistance = 400; // عرض البطاقة + الفجوة

        // ملء العروض المميزة
        function populatePremiumOffers() {
            const wrapper = document.getElementById('premiumOffersWrapper');
            wrapper.innerHTML = premiumOffers.map(offer =>
                '<div class="premium-offer-card" onclick="openPremiumOffer(' + offer.id + ')">' +
                    '<div class="premium-offer-content">' +
                        '<img src="images/New folder/' + (offer.image || 'placeholder.jpg') + '" alt="' + offer.title + '" class="premium-offer-image" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">' +
                        '<div class="premium-offer-image-placeholder" style="display: none;">' +
                            '<i class="' + offer.icon + '"></i>' +
                        '</div>' +
                        '<div class="premium-offer-overlay">' +
                            '<div class="premium-offer-title">' + offer.title + '</div>' +
                            '<div class="premium-offer-subtitle">' + offer.subtitle + '</div>' +
                        '</div>' +
                    '</div>' +
                    '<div class="premium-offer-footer">' +
                        '<div class="premium-badge">عرض مميز</div>' +
                    '</div>' +
                '</div>'
            ).join('');
            updatePremiumNavigationButtons();
        }

        // تحديث أزرار التنقل للعروض المميزة
        function updatePremiumNavigationButtons() {
            const prevBtn = document.getElementById('premiumPrevBtn');
            const nextBtn = document.getElementById('premiumNextBtn');
            const maxPosition = (premiumOffers.length - premiumVisibleCards) * premiumMoveDistance;
            prevBtn.disabled = premiumCurrentPosition <= 0;
            nextBtn.disabled = premiumCurrentPosition >= maxPosition;
        }

        // تحريك العروض المميزة
        function movePremiumOffers(direction) {
            const wrapper = document.getElementById('premiumOffersWrapper');
            const maxPosition = (premiumOffers.length - premiumVisibleCards) * premiumMoveDistance;
            if (direction === 'right' && premiumCurrentPosition < maxPosition) {
                premiumCurrentPosition += premiumMoveDistance;
            } else if (direction === 'left' && premiumCurrentPosition > 0) {
                premiumCurrentPosition -= premiumMoveDistance;
            }
            premiumCurrentPosition = Math.max(0, Math.min(premiumCurrentPosition, maxPosition));
            wrapper.style.transform = 'translateX(-' + premiumCurrentPosition + 'px)';
            updatePremiumNavigationButtons();
        }

        document.getElementById('premiumPrevBtn').addEventListener('click', function() { movePremiumOffers('left'); });
        document.getElementById('premiumNextBtn').addEventListener('click', function() { movePremiumOffers('right'); });
        document.addEventListener('DOMContentLoaded', populatePremiumOffers);
// تعريف دالة openServicePage لمنع الخطأ
function openServicePage(serviceName) {
    // يمكنك تخصيص هذا السلوك لاحقًا
    alert('تم اختيار الخدمة: ' + serviceName);
}
    </script>
<script>
// دالة قائمة رئيسية لمنع الخطأ
function toggleMainMenu() {}
</script>
    <!-- ملاحظة: يجب أن تكون جميع الصور موجودة فعليًا في المسارات المذكورة وبدون اختلاف في الاسم أو الامتداد -->
</body>
</html>